from typing import Any, Dict, List, Tuple

import numpy as np

from services.studio._text_to_workflow.common.schema import SubsetName, TargetFramework, WorkflowDict
from services.studio._text_to_workflow.common.state_store import StateBuilder, StateStore
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel
from services.studio._text_to_workflow.utils.workflow_utils import cleanup_workflow
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import core_wf_workflow_edit_score

from . import activity_summary_helpers, build_embeddings

train_subsets: dict[TargetFramework, tuple[SubsetName, ...]] = {
    "Portable": ("train",),
    "Windows": ("train",),
}


class ActivitySummaryRetriever:
    def __init__(self, config: Dict[str, Any], embedding_model: EmbeddingModel) -> None:
        self.summaries_index = Index(config, embedding_model)

    def get_relevant(
        self,
        workflow: WorkflowDict,
        queries: set[str],
        k: int | None = None,
        identifier: str | None = None,
    ) -> List[Dict[str, Any]]:
        relevant = list()
        documents, scores = self.summaries_index.search(workflow, queries, k=k, identifier=identifier)
        for document, score in zip(documents, scores, strict=False):
            document["similarity"] = score
            relevant.append(document)
        return relevant


class Index(StateBuilder):
    config: Dict[str, Any]
    retriever_config: Dict[str, Any]
    embedding_model: EmbeddingModel
    use_leave_one_out: bool
    ted_sample_size: int
    n_demonstrations: int

    def __init__(self, config: Dict[str, Any], embedding_model: EmbeddingModel) -> None:
        self.config = config
        self.embedding_model = embedding_model

        self.retriever_config = self.config.get("retriever", {})
        self.use_leave_one_out = self.config.get("use_leave_one_out", True)
        self.ted_sample_size = self.retriever_config.get("ted_sample_size", 30)
        self.n_demonstrations = self.retriever_config.get("demonstrations", 3)

        self.store = StateStore((paths.get_workflow_summarization_retriever_path() / "state.pkl").as_posix(), self, lazy_load=settings.DEBUG_MODE)

    def build(self) -> tuple[dict, dict]:
        documents, embeddings, keys, paths = [], [], [], []
        for target_framework, subsets in train_subsets.items():
            for subset in subsets:
                (
                    subset_documents,
                    subset_embeddings,
                    subset_keys,
                    subset_paths,
                ) = build_embeddings.run(self.config, target_framework, subset, settings.DEBUG_MODE)
                keys.extend(subset_keys)
                documents.extend(subset_documents)
                embeddings.append(subset_embeddings)
                paths.extend(subset_paths)
        state = {
            "paths": paths,
            "documents": documents,
            "embeddings": np.concatenate(embeddings, axis=0),
            "keys": keys,
        }
        state_info = {"documents": documents}
        print(f"Built activity summary index with {len(state['documents'])} documents.")
        return state, state_info

    def search(
        self,
        workflow: WorkflowDict,
        queries: set[str],
        k: int | None = None,
        identifier: str | None = None,
    ) -> Tuple[List[Dict[str, Any]], List[float]]:
        if k is None:
            k = self.n_demonstrations

        # compute similarity
        embedding = self.embedding_model.encode(activity_summary_helpers.get_aggregate_representation_activity_names(queries))
        embedding_cosine_score = self.store.state["embeddings"] @ embedding

        # # Alternative scores to use instead of embedding only
        # queries_keys = {activity_summary_helpers.get_key_representation_for_activity(query) for query in queries}
        # coverage = np.array([len(queries_keys & keys) / len(queries_keys) for keys in self.store.state["keys"]])
        # iou = np.array([len(queries_keys & keys) / len(queries_keys | keys) for keys in self.store.state["keys"]])
        # weights = np.array([0.3, 0.2, 0.5])
        # overall_score = weights.dot([coverage, iou, embedding_cosine_score])

        # compute TED
        subset_indices = np.argsort(embedding_cosine_score)[-self.ted_sample_size :]
        subset_ted_scores = []
        cleaned_workflow = cleanup_workflow(workflow)
        for index in subset_indices:
            path = self.store.state["paths"][index]
            assert path.exists(), f"Path {path} does not exist."
            demo_workflow = cleanup_workflow(yaml_load(path)["input"], shallow=True)
            ted_score, _, _, _ = core_wf_workflow_edit_score(cleaned_workflow, demo_workflow, "levenshtein", None)
            subset_ted_scores.append(ted_score)
        ted_scores = np.zeros_like(embedding_cosine_score)
        ted_scores[subset_indices] = subset_ted_scores

        # sample demonstrations
        documents: List[Dict[str, Any]] = []
        scores: List[float] = []
        for index in np.lexsort((embedding_cosine_score, ted_scores))[::-1]:
            if len(documents) >= k:
                break
            if self.use_leave_one_out and identifier is not None and self.store.state["paths"][index].stem == identifier:
                continue
            documents.append(self.store.state["documents"][index])
            scores.append(float(ted_scores[index]))
        return documents[::-1], scores[::-1]

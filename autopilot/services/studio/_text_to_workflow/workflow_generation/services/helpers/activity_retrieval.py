from dataclasses import dataclass

import langchain_core
import langchain_core.language_models
import numpy as np

from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflowDataPoint
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDefinition,
    Connection,
    TargetFramework,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.request_schema import ModelOptions
from services.studio._text_to_workflow.workflow_generation.config.constants import (
    ExcelPackageOption,
    MailPackageOption,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityRetrievalGeneration,
    ProposedActivity,
    WfGenDataPointV2,
)


class GenerationSettings:
    """Contains general user configurations that should be applied during the generation"""

    def __init__(
        self,
        model_options: dict[str, ModelOptions] | None,
        default_excel_package: ExcelPackageOption | None,
        default_mail_package: MailPackageOption | None,
    ):
        self.default_excel_package: ExcelPackageOption | None = default_excel_package
        self.default_mail_package: MailPackageOption | None = default_mail_package
        if model_options is None:
            model_options = {
                "retrieval": {"model_name": "activity_retrieval_gemini_model"},
                "generation": {"model_name": "workflow_generation_gemini_model"},
            }

        self.model_options: dict[str, ModelOptions] = model_options


class ActivityRetrievalParams:
    """Collection of common parameters used in the activity retrieval process"""

    def __init__(
        self,
        target_framework: TargetFramework,
        mode: ActivitiesGenerationMode | None,
        eval_mode: bool,
        connections: list[Connection],
        ignored_namespaces: set[str],
        ignored_activities: set[str],
        model: langchain_core.language_models.BaseChatModel,
    ):
        self.mode: ActivitiesGenerationMode | None = mode
        self.target_framework: TargetFramework = target_framework
        self.eval_mode = eval_mode
        self.connections = connections
        self.ignored_namespaces = ignored_namespaces
        self.ignored_activities = ignored_activities
        self.model = model


class ActivitiesProposal:
    """Contains the activities/triggers proposed to implement the automation described by a query
    Each proposal contains of a list of lists, the activities are grouped in inner lists ordered by their relevance.
    If 2 activities belong to the same group, they are equally relevant.
    """

    def __init__(
        self,
        query_proposal_activities: list[list[ProposedActivity]],
        workflow_proposal_activities: list[list[ProposedActivity]],
        query_proposal_triggers: list[list[ProposedActivity]],
        workflow_proposal_triggers: list[list[ProposedActivity]],
    ):
        # proposed activities/triggers derived from query embeddings
        self.query_proposal_activities = query_proposal_activities
        self.query_proposal_triggers = query_proposal_triggers
        # proposed activities/triggers derived from the existing workflow embeddings
        self.workflow_proposal_activities = workflow_proposal_activities
        self.workflow_proposal_triggers = workflow_proposal_triggers


@dataclass
class _ActivityRetrievalResult:
    generation: ActivityRetrievalGeneration
    prompt: str
    token_usage: TokenUsage
    proposed_triggers: list[ProposedActivity]
    proposed_activities: list[ProposedActivity]
    retrieved_triggers: list[str]
    retrieved_activities: list[str]
    ignored_activities: set[str]
    unprocessed_retrieved_activities: list[str]
    unprocessed_retrieved_triggers: list[str]
    generation_details: ActivitiesProposal
    query_embedding: np.ndarray
    connections_embedding: np.ndarray
    connections_by_key: dict[str, Connection]


class ActivityRetrievalResult:
    def __init__(
        self,
        generation: ActivityRetrievalGeneration,
        prompt: str,
        token_usage: TokenUsage,
        demonstrations: list[WfGenDataPointV2],
        proposed_triggers: list[ProposedActivity],
        proposed_activities: list[ProposedActivity],
        retrieved_triggers: list[str],
        retrieved_activities: list[str],
        ignored_activities: set[str],
        unprocessed_retrieved_triggers: list[str],
        unprocessed_retrieved_activities: list[str],
        generation_details: ActivitiesProposal,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        connections_by_key: dict[str, Connection],
    ):
        self.generation = generation
        self.prompt = prompt
        self.token_usage = token_usage
        self.demonstrations = demonstrations
        self.proposed_triggers = proposed_triggers
        self.proposed_activities = proposed_activities
        self.generation_details = generation_details
        self.unprocessed_retrieved_activities = unprocessed_retrieved_activities  # triggers retrieved directly by LLM, no additional processing
        self.unprocessed_retrieved_triggers = unprocessed_retrieved_triggers  # activities retrieved directly by LLM, no additional processing
        self.retrieved_activities = retrieved_activities
        self.retrieved_triggers = retrieved_triggers
        self.ignored_activities = ignored_activities
        self.query_embedding = query_embedding
        self.connections_embedding = connections_embedding
        self.connections_by_key = connections_by_key

    @classmethod
    def _from_internal_result(
        cls,
        internal_result: _ActivityRetrievalResult,
        demonstrations: list[WfGenDataPointV2],
    ) -> "ActivityRetrievalResult":
        return cls(
            generation=internal_result.generation,
            prompt=internal_result.prompt,
            token_usage=internal_result.token_usage,
            demonstrations=demonstrations,
            proposed_triggers=internal_result.proposed_triggers,
            proposed_activities=internal_result.proposed_activities,
            retrieved_triggers=internal_result.retrieved_triggers,
            retrieved_activities=internal_result.retrieved_activities,
            ignored_activities=internal_result.ignored_activities,
            unprocessed_retrieved_triggers=internal_result.unprocessed_retrieved_triggers,
            unprocessed_retrieved_activities=internal_result.unprocessed_retrieved_activities,
            generation_details=internal_result.generation_details,
            query_embedding=internal_result.query_embedding,
            connections_embedding=internal_result.connections_embedding,
            connections_by_key=internal_result.connections_by_key,
        )


class APIActivityRetrievalResult:
    def __init__(
        self,
        generation: ActivityRetrievalGeneration,
        prompt: str,
        token_usage: TokenUsage,
        demonstrations: list[ApiWorkflowDataPoint],
        proposed_activities: list[ProposedActivity],
        retrieved_activities: list[str],
        ignored_activities: set[str],
        unprocessed_retrieved_activities: list[str],
        generation_details: ActivitiesProposal,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        connections_by_key: dict[str, Connection],
    ):
        self.generation = generation
        self.prompt = prompt
        self.token_usage = token_usage
        self.demonstrations = demonstrations
        self.proposed_activities = proposed_activities
        self.generation_details = generation_details
        self.unprocessed_retrieved_activities = unprocessed_retrieved_activities  # triggers retrieved directly by LLM, no additional processing
        self.retrieved_activities = retrieved_activities
        self.ignored_activities = ignored_activities
        self.query_embedding = query_embedding
        self.connections_embedding = connections_embedding
        self.connections_by_key = connections_by_key

    @classmethod
    def _from_internal_result(
        cls,
        internal_result: _ActivityRetrievalResult,
        demonstrations: list[ApiWorkflowDataPoint],
    ) -> "APIActivityRetrievalResult":
        return cls(
            generation=internal_result.generation,
            prompt=internal_result.prompt,
            token_usage=internal_result.token_usage,
            demonstrations=demonstrations,
            proposed_activities=internal_result.proposed_activities,
            retrieved_activities=internal_result.retrieved_activities,
            ignored_activities=internal_result.ignored_activities,
            unprocessed_retrieved_activities=internal_result.unprocessed_retrieved_activities,
            generation_details=internal_result.generation_details,
            query_embedding=internal_result.query_embedding,
            connections_embedding=internal_result.connections_embedding,
            connections_by_key=internal_result.connections_by_key,
        )


class ActivityRetrievalWorkflowDetails:
    def __init__(
        self,
        has_trigger: bool,
        ground_truth_trigger: ActivityDefinition | None,
        ground_truth_activities: list[ActivityDefinition],
        embedding_steps: list[str],
    ):
        self.ground_truth_trigger = ground_truth_trigger
        self.ground_truth_activities = ground_truth_activities
        self.has_trigger = has_trigger
        self.embedding_steps = embedding_steps

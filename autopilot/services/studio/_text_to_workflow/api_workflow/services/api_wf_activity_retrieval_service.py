import json
import pathlib
import typing as t

import numpy as np

from services.studio._text_to_workflow.common.api_activity_retriever import (
    APIActivitiesRetriever,
)
from services.studio._text_to_workflow.common.api_workflow.schema import NON_DYNAMIC_ACTIVITY_TYPES, ApiWorkflow, ApiWorkflowDataPoint
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import list_activities
from services.studio._text_to_workflow.common.constants import DAP_NAMESPACE
from services.studio._text_to_workflow.common.schema import (
    ActivityDefinition,
    Connection,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.inference.llm_schema import (
    ConsumingFeatureType,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import (
    api_workflow_generation_retrievers,
)
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import (
    DEFAULT_RETRIEVAL_MODEL_NAME,
)
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import (
    ConnectionEmbeddingsRetriever,
)
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import (
    ActivityRetrievalParams,
    ActivityRetrievalWorkflowDetails,
    APIActivityRetrievalResult,
)
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalServiceBase,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ProposedActivity,
)

LOGGER = AppInsightsLogger()


class APIActivityRetrievalService(WorkflowGenerationActivityRetrievalServiceBase[APIActivitiesRetriever]):
    def __init__(
        self,
        activities_retriever: APIActivitiesRetriever,
        connection_embeddings_retriever: ConnectionEmbeddingsRetriever,
    ):
        super().__init__(connection_embeddings_retriever, activities_retriever)

        # TO DO: this should be a parameter
        config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "api_activity_retrieval_prompt.yaml"
        self.config: dict[str, t.Any] = yaml_load(config_path)

        self.post_processing_config = self.config["post_processing"]
        self.prompt_config = self.config["prompt"]
        self.demonstrations_config = self.config["demonstration_ranking"]

        self.demo_retriever = api_workflow_generation_retrievers.APIWorkflowDemonstrationsRetriever(
            # TO DO: pass the config object here, instead of the path
            config_path.as_posix(),
            paths.get_api_workflow_retriever_dataset_path(),
            paths.get_api_workflow_demonstrations_retriever_path(),
            activities_retriever,
        )

    def _format_activity_definitions(self, activities: dict[int, ProposedActivity]) -> str:
        return "\n".join(
            [
                json.dumps(
                    {
                        "id": activity_id,
                        "description": activity["description"],
                        "display_name": activity["display_name"],
                        "category": activity["category"],
                    }
                )
                for activity_id, activity in activities.items()
            ]
        )

    async def generate_relevant_activities(
        self,
        query: str,
        workflow: t.Optional[ApiWorkflow],
        connections: list[Connection],
        eval_mode: bool = False,
    ) -> APIActivityRetrievalResult:
        model = ModelManager().get_llm_model(
            DEFAULT_RETRIEVAL_MODEL_NAME,  # for now we use the same default as the workflow generation
            ConsumingFeatureType.WORKFLOW_GENERATION,
        )

        params: ActivityRetrievalParams = ActivityRetrievalParams(
            target_framework="Api",
            mode=None,  # for API workflows, the mode is irrelevant for the activity retrieval
            connections=connections,
            ignored_namespaces=set(),
            ignored_activities=set(),
            eval_mode=eval_mode,
            model=model,
        )

        workflow_details = self._parse_workflow(workflow) if workflow else None

        (
            activities_proposal,
            query_embedding,
            connections_embedding,
            connections_by_key,
            existing_workflow_namespaces,
        ) = self._prepare_generate_relevant_activities(query, params, workflow_details)

        demonstrations = self._get_relevant_demonstrations(query, query_embedding, connections_embedding, params)
        demo_query_details = [(demo.query, demo.plan) for demo in demonstrations]

        result = await self._generate_relevant_activities(
            query,
            demo_query_details,
            query_embedding,
            connections_embedding,
            params,
            activities_proposal,
            existing_workflow_namespaces,
            "Api",
            connections_by_key,
            prompt_entity_types="activities",
        )
        connector_keys = set()
        for activity in result.retrieved_activities:
            activity_definition: ActivityDefinition | None = self.activities_retriever.get(activity)
            if activity_definition is not None:
                connector_keys.add(activity_definition.get("connectorKey"))

        result.retrieved_activities = list(
            set(
                result.retrieved_activities
                + [act["fullClassName"] for act in self.activities_retriever.get_basic_http_activity_by_connectors_key(connector_keys)]
            )
        )

        return APIActivityRetrievalResult._from_internal_result(result, demonstrations)

    def _parse_workflow(self, workflow: ApiWorkflow) -> ActivityRetrievalWorkflowDetails:
        """Get activities and triggers from a given workflow, validate their existence, and extract their display names for embedding."""

        has_trigger = False
        ground_truth_trigger: ActivityDefinition | None = None

        steps_to_embed: list[str] = []

        existing_activities = list_activities(workflow)

        # we build a pseudo-plan with the thought of each retrieved activity
        for activity in existing_activities:
            steps_to_embed.append(activity.thought)

        # We ensure that we have all the retrieved activities for the workflow representing the ground truth
        ground_truth_activities: list[ActivityDefinition] = []
        for activity in existing_activities:
            if activity.activity in NON_DYNAMIC_ACTIVITY_TYPES:
                continue

            full_activity_class_name = DAP_NAMESPACE + "." + activity.activity
            activity_definition = self.activities_retriever.get(full_activity_class_name, "activity")
            if activity_definition is not None:
                ground_truth_activities.append(activity_definition)
            else:
                print(f"⚠️ Activity {activity.activity} not found")  # Warning

        return ActivityRetrievalWorkflowDetails(has_trigger, ground_truth_trigger, ground_truth_activities, steps_to_embed)

    def _get_relevant_demonstrations(
        self,
        query: str,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        params: ActivityRetrievalParams,
    ) -> list[ApiWorkflowDataPoint]:
        demonstrations = self.demo_retriever.get_relevant(
            query,
            query_embedding,
            connections_embedding,
            params.ignored_namespaces,
            set(),
            params.ignored_activities,
        )

        demonstration_details: list[dict] = [demo for demos in demonstrations.values() for demo in demos]

        # if we're doing an eval, we don't want to include the current query in the demonstrations
        if params.eval_mode:
            demonstration_details = [demo for demo in demonstration_details if demo["query"] != query]

        return [ApiWorkflowDataPoint.model_validate(demo) for demo in demonstration_details]

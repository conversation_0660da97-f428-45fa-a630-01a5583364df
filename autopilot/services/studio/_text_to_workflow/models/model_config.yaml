####################
# EMBEDDING MODELS #
####################

embedding_model:
  model_type: sentence-embedding
  deployment_name: all-mpnet-base-v2

activities_embedding_model:
  model_type: sentence-embedding
  deployment_name: uipath/stella-trained

rerank_model:
  deployment_name: cross-encoder/ms-marco-MiniLM-L-6-v2

bpmn_embedding_model:
  model_type: sentence-embedding
  deployment_name: uipath/stella-trained

###################################
# GENERATION MODEL CONFIGURATIONS #
###################################

# Note:
# Overridden parameters are merged with the parent configuration,
# and fully override the parent's parameters, including for dictionaries.

base_anthropic_generation_model:
  type: anthropic
  name: anthropic.claude-3-5-sonnet-20240620-v1:0
  params:
    temperature: 0.0
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
    max_tokens: 1000 # Total output tokens
    max_total_tokens: 1000000 # placeholder value

base_azureopenai_generation_model:
  type: azureopenai
  name: gpt-4o-mini-2024-07-18
  params:
    openai_api_version: 2025-01-01-preview
    temperature: 0.0
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
    request_timeout: 60
    max_tokens: 1000 # Total output tokens (max for model is 16384 but we limit to 1000 by default to improve performance)
    max_retries: 2
    max_total_tokens: 128000 # Total tokens for the prompt and the response

base_azureopenai_reasoning_generation_model:
  type: azureopenai
  name: o3-mini-2025-01-31
  params:
    openai_api_version: 2025-01-01-preview
    reasoning_effort: low
    request_timeout: 120
    max_retries: 1
    max_total_tokens: 200000 # Total tokens for the prompt and the response. Note that max_tokens is not supported for reasoning models.

base_googlevertexai_generation_model:
  type: googlevertexai
  name: gemini-2.0-flash-001
  params:
    temperature: 0.0
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
    request_timeout: 60
    max_tokens: 1000 # Total output tokens (max for model is 8192 but we limit to 1000 by default to improve performance)
    max_retries: 2
    max_total_tokens: 1000000 # Total tokens for the prompt and the response

base_googlevertexai_reasoning_generation_model:
  type: googlevertexai
  name: gemini-2.5-pro-preview-05-06
  params:
    temperature: 0.0
    request_timeout: 120
    max_retries: 1
    max_total_tokens: 1000000 # Total tokens for the prompt and the response

###################
# MODEL SELECTION #
###################

# Activity Configuration
activity_config_model:
  inherits: base_googlevertexai_generation_model
  params:
    max_tokens: 250
    request_timeout: 60
    top_p: 0.1

# Activity Summary
activity_summary_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 4000
    max_total_tokens: 65536

# Agent Evaluation
agent_evaluation_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 16384
    temperature: 0.3
    top_p: 0.9

agent_generation_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 1000
    max_total_tokens: 100000 # Total tokens for the prompt and the response

# BPMN Generation
bpmn_generation_chat_model_openai:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    temperature: 0.2
    top_p: 0.9
    max_tokens: 12800
    request_timeout: 120
    max_retries: 2

bpmn_generation_chat_model_openai_mini:
  inherits: base_azureopenai_generation_model
  params:
    temperature: 0.2
    top_p: 0.9
    max_tokens: 4096
    request_timeout: 120
    max_retries: 2

bpmn_generation_chat_model_anthropic:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    max_tokens: 4096
    request_timeout: 120
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    max_retries: 2

bpmn_generation_chat_model_google:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.5-pro-preview-05-06
  params:
    temperature: 0.2
    top_p: 0.9
    max_tokens: 12800
    request_timeout: 120
    max_retries: 2    

bpmn_generation_chat_model_google_flash:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    temperature: 0.2
    top_p: 0.9
    max_tokens: 4096
    request_timeout: 120
    max_retries: 2

# BPMN Generation Evals
bpmn_generation_eval_model_anthropic:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    max_tokens: 4096
    request_timeout: 120
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    max_retries: 2

# BPMN QA Evals
bpmn_qa_eval_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 1000
    request_timeout: 30
    max_retries: 2

# BPMN Extension Evals
bpmn_extension_eval_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 1000
    request_timeout: 30
    max_retries: 2

bpmn_router_model_openai:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 12800
    request_timeout: 30
    max_retries: 2
    temperature: 0.2
    top_p: 0.9

bpmn_router_model_google:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 1000
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    temperature: 0.2
    top_p: 0.9

# BPMN Planner
bpmn_planner_model_openai:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 12800
    request_timeout: 30
    max_retries: 2
    temperature: 0.2
    top_p: 0.9

bpmn_planner_model_anthropic:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    max_tokens: 12800
    request_timeout: 120
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    max_retries: 2

bpmn_planner_model_google:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.5-pro-preview-05-06
  params:
    temperature: 0.2
    top_p: 0.9
    max_tokens: 12800
    request_timeout: 120
    max_retries: 2 

# BPMN Telemetry
bpmn_telemetry_model_google:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 1000
    request_timeout: 60
    max_total_tokens: 65536
    temperature: 0.2
    top_p: 0.9

# Code Generation
code_generation_retrieval_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 1000
    request_timeout: 60

code_generation_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 2000
    request_timeout: 60

# Expression Generation
expression_generation_model:
    inherits: base_googlevertexai_generation_model
    name: gemini-2.0-flash-001
    params:
      max_tokens: 2000
      request_timeout: 30
      max_total_tokens: 32768 # Total tokens for the prompt and the response
      response_mime_type: application/json

expression_generation_bpmn_model:
    inherits: base_googlevertexai_generation_model
    name: gemini-2.0-flash-001
    params:
      max_tokens: 2000
      request_timeout: 30
      max_total_tokens: 32768 # Total tokens for the prompt and the response

extension_input_generation_bpmn_model:
    inherits: base_googlevertexai_generation_model
    name: gemini-2.0-flash-001
    params:
      max_tokens: 2000
      request_timeout: 30
      max_total_tokens: 32768 # Total tokens for the prompt and the response

expression_generation_bpmn_model_google:
    inherits: base_googlevertexai_generation_model
    name: gemini-2.0-flash-001
    params:
      max_tokens: 2000
      request_timeout: 30
      max_total_tokens: 32768 # Total tokens for the prompt and the response

expression_generation_bpmn_model_openai:
    inherits: base_azureopenai_generation_model
    name: gpt-4.1-mini-2025-04-14
    params:
      max_tokens: 2000
      request_timeout: 30
      max_total_tokens: 32768

expression_generation_large_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 1000
    request_timeout: 30
    max_total_tokens: 32768 # Total tokens for the prompt and the response
    additional_kwargs:
      response_format:
        type: json_object

# Semantic Diff
semantic_diff_model:
  inherits: base_googlevertexai_generation_model
  params:
    max_tokens: 4096
    request_timeout: 60
    max_total_tokens: 32768
    max_retries: 0 # No auto-retries, as we do it in the endpoint
    response_mime_type: application/json

# Test Data Generation
testdata_generation_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    temperature: 0.9
    max_tokens: 2000
    top_p: 0.2
    frequency_penalty: 0.0
    presence_penalty: 0.0

testdata_generation_extra_data_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 1000
    top_p: 0.5
    temperature: 0.8
    frequency_penalty: 0.3
    presence_penalty: 0.0

# Translation
translation_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 1000
    request_timeout: 20

# Activity Retrieval
activity_retrieval_openai_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 350
    request_timeout: 60
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
    additional_kwargs:
      response_format:
        type: json_object

activity_retrieval_claude_model:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    request_timeout: 120
    max_tokens: 350
    max_total_tokens: 350 # Total tokens for the prompt and the response
    additional_kwargs:
      response_format:
        type: json_object

activity_retrieval_gemini_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 350
    request_timeout: 60
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
    response_mime_type: application/json

agent_retrieval_gemini_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 250
    request_timeout: 60
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
    response_mime_type: application/json

activity_retrieval_openai_reasoning_model:
  inherits: base_azureopenai_reasoning_generation_model
  name: o3-mini-2025-01-31
  params:
    reasoning_effort: low
    request_timeout: 120
    max_retries: 1
    additional_kwargs:
      response_format:
        type: json_object

# Predict Expression
predict_expression_gemini_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 350
    request_timeout: 10
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    top_p: 0.95
    temperature: 0.9

predict_expression_gpt4oaug_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 350
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0


# Draft Generation
workflow_draft_generation_gemini_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: null # Set at runtime
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response


workflow_draft_generation_gpt41_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4.1-2025-04-14
  params:
    max_tokens: null # Set at runtime
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response


workflow_draft_generation_large_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: null # Set at runtime
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_draft_generation_claude_model:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    max_tokens: null # Set at runtime
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    additional_headers:
      X-UIPATH-AWS-BEDROCK-REGION: eu-central-1

# Workflow Generation
workflow_generation_large_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: null # Set at runtime
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_generation_gemini_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: null # Set at runtime
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_generation_reasoning_model:
  inherits: base_azureopenai_reasoning_generation_model
  name: o3-mini-2025-01-31
  params:
    reasoning_effort: low
    request_timeout: 120
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_generation_medium_reasoning_model:
  inherits: base_azureopenai_reasoning_generation_model
  name: o3-mini-2025-01-31
  params:
    reasoning_effort: medium
    request_timeout: 120
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_generation_long_reasoning_model:
  inherits: base_azureopenai_reasoning_generation_model
  name: o3-mini-2025-01-31
  params:
    reasoning_effort: high
    request_timeout: 180
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_generation_claude_model:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    request_timeout: 120
    max_tokens: null # Set at runtime
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_generation_claude_reasoning_model:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    additional_model_request_fields:
      reasoning_config:
        type: enabled
        budget_tokens: 1024
    max_tokens: null # Set at runtime
    request_timeout: 60
    temperature: 1
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_fix_4o_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    request_timeout: 60
    max_retries: 0
    max_tokens: null
    # max_tokens: 16384 # Also set at runtime, acts as a fallback and comparison point
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_fix_41_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4.1-2025-04-14
  params:
    request_timeout: 60
    max_retries: 0
    max_tokens: null # set at runtime, acts as a fallback and comparison point
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_fix_41_mini_model:
  inherits: base_azureopenai_generation_model
  name: gpt-41-mini-2025-04-14
  params:
    request_timeout: 60
    max_retries: 0
    max_tokens: null # set at runtime, acts as a fallback and comparison point
    max_total_tokens: 65536 # Total tokens for the prompt and the response

workflow_fix_gemini_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: null # Set at runtime
    request_timeout: 60
    max_total_tokens: 65536
    max_retries: 0
    # response_validation: false  # does not work

workflow_fix_claude37_model:
  inherits: base_anthropic_generation_model
  name: anthropic.claude-3-7-sonnet-20250219-v1:0
  params:
    # additional_model_request_fields:
    #   reasoning_config:
    #     type: enabled
    #     budget_tokens: 1024
    max_tokens: null # Set at runtime
    request_timeout: 60
    # temperature: 1
    max_total_tokens: 65536

workflow_fix_reasoning_model:
  inherits: base_azureopenai_reasoning_generation_model
  name: o3-mini-2025-01-31
  params:
    reasoning_effort: low
    request_timeout: 60
    max_total_tokens: 65536 # Total tokens for the prompt and the response
    max_tokens: null

# Workflow Assistant
workflow_assistant_gemini_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    request_timeout: 60
    max_tokens: 500
    max_retries: 0 # No retries, as we may want to increase the max_tokens in P99 use-cases for very complex queries

workflow_assistant_openai_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    request_timeout: 60
    max_tokens: 500
    max_retries: 0 # No retries, as we may want to increase the max_tokens in P99 use-cases for very complex queries
    max_total_tokens: 128000 # Total tokens for the prompt and the response

workflow_analyzer_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    request_timeout: 60
    max_tokens: 500
    max_retries: 0 # No retries, as we may want to increase the max_tokens in P99 use-cases for very complex queries
    max_total_tokens: 128000 # Total tokens for the prompt and the response

api_activity_edit_openai_reasoning_model:
  inherits: base_azureopenai_reasoning_generation_model
  name: o3-mini-2025-01-31
  params:
    reasoning_effort: low
    max_tokens: 1500
    max_total_tokens: 128000
    request_timeout: 60
    max_retries: 0

api_activity_edit_openai_model:
  inherits: base_azureopenai_generation_model
  name: gpt-4o-2024-08-06
  params:
    max_tokens: 1500
    max_total_tokens: 128000
    request_timeout: 60
    max_retries: 0

# Query Extension
api_workflow_query_extension_model:
  inherits: base_googlevertexai_generation_model
  name: gemini-2.0-flash-001
  params:
    max_tokens: 1024
    request_timeout: 60
    top_p: 0.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
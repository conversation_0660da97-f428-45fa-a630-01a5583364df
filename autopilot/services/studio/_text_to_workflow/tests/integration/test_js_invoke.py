import pytest

from services.studio._text_to_workflow.expression_generation import expression_generation_endpoint
from services.studio._text_to_workflow.schemas import RequestContext
from services.studio._text_to_workflow.tests.fixtures.expression_generation_fixtures.js_invoke_inputs import examples
from services.studio._text_to_workflow.tests.utils.similarity_helper import text_similarity
from services.studio._text_to_workflow.utils.request_utils import set_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


@pytest.mark.asyncio
async def test_js_invoke_expressions():
    set_request_context(RequestContext())
    for test_case in examples:
        response = await expression_generation_endpoint.generate(test_case)
        result = yaml_load(response["result"])

        assert result["expression"] is not None
        assert result["explanation"] is not None
        assert "return" in result["expression"], "JSInvoke answer must end with a return statement"
        assert text_similarity(result["expression"].strip(), test_case["benchmarkExpression"]) > 0.55

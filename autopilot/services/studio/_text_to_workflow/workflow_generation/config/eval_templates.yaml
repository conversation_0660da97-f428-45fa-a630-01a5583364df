common:
  html_template_start: |-
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body{
                font-family: Arial, sans-serif;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 50px 0px;
                color: #333;
            }
            .fixed-header
            {
              position: sticky;
              top: 0;
            }
            .secondary-fixed-header
            {
              position: sticky;
              top: 58px;
            }
            .inline-header {
              display: inline-block;
            }
            th, td {
                border: 1px solid #ccc;
                padding: 10px;
                text-align: left;
            }
            th {
                background-color: #f8f8f8;
                color: #444;
                text-transform: uppercase;
                font-size: small;
            }
            .large-column {
              width: 20%;
            }
            .xl-column {
              width: 40%;
            }
            .small-text {
              font-size: 11px;
            }
            .averages {
              border-top: 3px solid #444;
              background-color: #f0f0f0;
            }
            .sample-details-button {
              cursor:pointer;
              color:blue;
              text-decoration:underline;
            }
            .small-header {
              font-weight:550;
            }
            .data-list {
              padding: 10px 15px;
            }
            .row-highlight > td {
              background: #e0f7fa;
            }
            .row-highlight.secondary-row > td {
              background: #b2dfdb;
            }
            td.xs-numeric-column,
            th.xs-numeric-column
            {
              max-width: 70px;
              overflow: hidden;
              padding: 5px;
            }
            .emphasis-row td
            {
              background:#f5c6c4;
            }
            .emphasis-row td,
            {
              font-weight:550;
              color: #444;
            }
            .section-header {
                background-color: #f8f8f8;
                color: #444;
                text-transform: uppercase;
            }
            .small-section-header {
              font-weight:550;
            }
            p.small-section-header {
              margin: 5px 10px;
            }
            .horizontally-padded {
                padding: 0px 15px;
            }
            .data-point {
              padding: 10px 15px;
            }
            table.details-table {
              margin: 5px 10px;
              color: #555;
            }
            details {
              margin: 10px 5px;
            }
            .details-table .row-highlight > td {
              background: #fef2f2;
              vertical-align: top;
              font-size: smaller;
            }
            .details-table .row-highlight > th {
              position: sticky;
              background: lightgoldenrodyellow;
              font-size: small;
            }
            .filter-button {
              border: 2px solid #04AA6D;
              background-color: #04AA6D; /* Green */
              color: white;
              padding: 15px 32px;
              text-align: center;
              text-decoration: none;
              display: inline-block;
              font-size: 16px;
              transition-duration: 0.2s;
              cursor: pointer;
            }
            .filter-button:hover {
              border: 2px solid #04AA6D;
              background-color: white;
              color: #04AA6D;
            }
            .script-column {
              max-width: 500px;
              overflow-wrap: break-word;
              word-break: break-word;
              width: 500px;
              white-space: pre-wrap;
            }
            pre.focused-script {
              padding: 10px 5px;
              border: 2px solid #ccc;
              margin: 0px 10px;
              background: #fef2f2;
              max-width: 100%;
              width: auto;
            }
        </style>
    </head>
  html_template_end: |-
    <script>
    var detailsButtons = document.getElementsByClassName("sample-details-button");
    var isOnlyHighCount = false;

    var detailsButtons = document.getElementsByClassName("sample-details-button");
    for (var i = 0; i < detailsButtons.length; i++)
    {
      detailsButtons[i].addEventListener("click", function()
      {
        this.classList.toggle("active");
        var content = this.parentNode.nextElementSibling;
        if (content.style.display === "table-row")
        {
            content.style.display = "none";
          }
        else
        {
          content.style.display = "table-row";
        }
      });
    }

    function get_row_with_low_TED(uiElement)
    {
      var activitiesCount = parseInt(uiElement.getElementsByClassName("ted-score-container")[0].textContent);
      return activitiesCount<0.8;
    }
    function get_row_with_large_act_count(uiElement)
    {
      var activitiesCount = parseInt(uiElement.getElementsByClassName("raw-act-count")[0].textContent);
      return activitiesCount>30;
    }

    function apply_toggle_on_samples(should_always_be_visible)
    {
      //hide all secondary rows (they contain detail information)
      document.querySelectorAll("#samples-table .secondary-row").forEach(function(item){
        item.style.display = "none";
      });

      // hide the actual activity rows
      document.querySelectorAll("#samples-table .main-row").forEach(function(item){

      if(should_always_be_visible(item))
        return;

      if (!isOnlyHighCount)
      {
        item.style.display = "none";
      }
      else
      {
        item.style.display = "table-row";
      }
      });
      isOnlyHighCount = !isOnlyHighCount;
    }

    //logic to toggle rows with high count of returned activities
    document.getElementById("activity-count-filter")?.addEventListener("click", () => apply_toggle_on_samples(get_row_with_large_act_count));

    //logic to toggle rows with low ted
    document.getElementById("low-ted-filter")?.addEventListener("click", () => apply_toggle_on_samples(get_row_with_low_TED));
    </script>
    </body>
    </html>
  parsing_exceptions_section_template: |-
    <h3 class="section-header">Parsing Exceptions</h3>
    <p class="small-section-header">Total number of parsing exceptions: {exception_count}</p>
    <table class="secondary-info-table">
      <tr class="fixed-header fixed-header">
          <th>Query</th>
          <th>Output</th>
      </tr>
      {exception_rows}
    </table>
  parsing_exception_row_template: |-
    <tr class="row-highlight">
      <td class="title-cell sample-details-button">{query}</td>
      <td class="small-text"><pre class="script-column">{output}</pre></td>
    </tr>
draft_generation:
  body_template_start: |-
    <body>
        <h3 class="section-header">Main Dataset analysis</h3>
        <div>
          <input type="button" id="low-ted-filter" class="filter-button" value="Toggle Rows with low TED">
        </div>
        <table id="samples-table">
            <tr class="fixed-header">
                <th class="large-column">Sample Name</th>
                <th class="xs-numeric-column">Elapsed Time</th>
                <th class="xs-numeric-column">Lev.</th>
                <th class="xs-numeric-column">Cleaned Wf Lev.</th>
                <th class="xs-numeric-column">Activity Tree Accuracy</th>
                <th class="xs-numeric-column">Activity Tree Edit Distance</th>
                <th class="xs-numeric-column">Lev. Params Score</th>
                <th class="xs-numeric-column">TED with Lev. Params</th>
                <th class="xs-numeric-column">Exact Params</th>
                <th class="xs-numeric-column">TED with Exact Params</th>
                <th class="large-column">Generation Details</th>
            </tr>
  sample_row_template: |-
    <tr class="row-highlight main-row">
      <td class="large-column sample-details-button">{query}</td>
      <td class="xs-numeric-column">{elapsed_time}</td>
      <td class="xs-numeric-column">{lev_score}</td>
      <td class="xs-numeric-column">{lev_score_no_extras}</td>
      <td class="xs-numeric-column">{activity_tree_accuracy}</td>
      <td class="xs-numeric-column ted-score-container">{ted_score}</td>
      <td class="xs-numeric-column">{levenstein_params_score}</td>
      <td class="xs-numeric-column">{ted_levenstein_params_score}</td>
      <td class="xs-numeric-column">{exact_params_score}</td>
      <td class="xs-numeric-column">{ted_exact_params_score}</td>
      <td class="small-text xl-column">
        <p class="small-section-header">Token Usage:</p>
        <p class="horizontally-padded">{token_usage}</p>
        <p class="small-section-header">TED Comparison:</p>
        <pre class="script-column focused-script">{ted_nodes_mapping}</pre>
        <p class="small-section-header">Missing From Retrieval:</p>
        <p class="horizontally-padded">{missed_activities}</p>
      </td>
    </tr>
    <tr class="row-highlight secondary-row" style="display:none">
      <td colspan="13">
        <details open>
          <summary><h3 class="small-section-header inline-header">Generated Workflow</h3></summary>
          <table class ="details-table">
            <tr class="row-highlight secondary-fixed-header">
                <th>Ground Truth</th>
                <th>Processed Generated Workflow</th>
                <th>Generated Workflow</th>
                <th>Existing Workflow</th>
            </tr>
            <tr class="row-highlight">
              <td><pre class="script-column">{gt_wf}</pre></td>
              <td><pre class="script-column">{gen_wf_processed}</pre></td>
              <td><pre class="script-column">{gen_wf}</pre></td>
              <td><pre class="script-column">{existing_workflow}</pre></td>
            </tr>
          </table>
        </details>
        <h3 class="small-section-header">Generation Details</h3>
        <details>
          <summary><span class="small-section-header">Post Generation Errors:</span></summary>
          <pre class="script-column focused-script">{post_generation_errors}</pre>
        </details>
        <details>
          <summary><span class="small-section-header">Prompt:</span></summary>
          <pre class="script-column focused-script">{prompt}</pre>
        </details>
        <details>
          <summary><span class="small-section-header">Raw Model Prediction:</span></summary>
          <pre class="script-column focused-script">{raw_model_prediction}</pre>
        </details>
        <h3 class="small-section-header">Demonstrations</h3>
        {demonstrations}
      </td>
    </tr>
  sample_row_demonstrations_template: |-
    <details>
        <summary>{query} - <b>{type}</b> demo</summary>
        <div class="data-point"><span class="small-header">Plan<i>(obsolete)</i>:</span><br>{plan}</div>
        <div class="data-point"><span class="small-header">Workflow:</span></div>
        <pre class="script-column focused-script">{solution_workflow}</pre>
    </details>
  averages_row_template: |-
    <tr class="emphasis-row">
      <td>Averages</td>
      <td class="xs-numeric-column">{elapsed_time}</td>
      <td class="xs-numeric-column">{lev_score}</td>
      <td class="xs-numeric-column">{lev_score_no_extras}</td>
      <td class="xs-numeric-column">{activity_tree_accuracy}</td>
      <td class="xs-numeric-column">{ted_score}</td>
      <td class="xs-numeric-column">{levenstein_params_score}</td>
      <td class="xs-numeric-column">{ted_levenstein_params_score}</td>
      <td class="xs-numeric-column">{exact_params_score}</td>
      <td class="xs-numeric-column">{ted_exact_params_score}</td>
      <td>N/A</td>
    </tr>
  general_info_template: |-
      </table>
      {exceptions_section}
      <h3 class="section-header">General Information</h3>
      <div class="data-point"><span class="small-header">Prompt Template</span></div>
      <div class="data-point">{prompt}</div>
      <div class="data-point"><span class="small-header">Model Details Activities</span></div>
      <div class="data-point">{model}</div>
      <div class="data-point"><span class="small-header">Demonstration Ranking Details</span></div>
  errors_row_template: |-
    <tr class="row-highlight">
      <td class="title-cell sample-details-button">{query}</td>
      <td class="small-text">{exceptions}</td>
    </tr>
    <tr class="row-highlight secondary-row" style="display:none">
      <td colspan="3">
        <table class ="details-table">
          <tr class="row-highlight secondary-fixed-header">
              <th>Ground Truth</th>
              <th>Processed Generated Workflow</th>
          </tr>
          <tr class="row-highlight">
            <td><pre class="script-column">{gt_wf}</pre></td>
            <td><pre class="script-column">{gen_wf_processed}</pre></td>
          </tr>
        </table>
      </td>
    </tr>
activity_retrieval:
  body_template_start: |-
    <body>
        <h3 class="section-header">Main Dataset analysis</h3>
        <div>
          <input type="button" id="activity-count-filter" class="filter-button" value="Toggle >30 retrieved activities">
        </div>
        <table id="samples-table">
            <tr class="fixed-header">
                <th class="large-column" >Sample Name</th>
                <th class="xs-numeric-column">Elapsed Time</th>
                <th class="xs-numeric-column">Ambiguities Similarity</th>
                <th class="xs-numeric-column">Plan Similarity</th>
                <th class="xs-numeric-column">Score Precision</th>
                <th class="xs-numeric-column">Extended Trigger Precision</th>
                <th class="xs-numeric-column">Raw Trigger Recall</th>
                <th class="xs-numeric-column">Extended Trigger Recall</th>
                <th class="xs-numeric-column">Ideal Plan Trigger Recall</th>
                <th class="xs-numeric-column">Raw Activities Precision</th>
                <th class="xs-numeric-column">Extended Activities Precision</th>
                <th class="xs-numeric-column">Raw Activities Recall</th>
                <th class="xs-numeric-column">Extended Activities Recall</th>
                <th class="xs-numeric-column">Ideal Plan Activities Recall</th>
                <th>Gen. Details</th>
                <th>Last Relevant Index</th>
                <th class="large-column">Missed Activities</th>
            </tr>
  sample_row_template: |-
    <tr class="row-highlight main-row">
      <td class="sample-details-button">{query}</td>
      <td class="xs-numeric-column small-text">{elapsed_time}</td>
      <td class="xs-numeric-column small-text">{ambiguities_similarity}</td>
      <td class="xs-numeric-column small-text">{plan_similarity}</td>
      <td class="xs-numeric-column small-text">{score_precision}</td>
      <td class="xs-numeric-column small-text">{extended_trigger_precision}</td>
      <td class="xs-numeric-column small-text">{raw_trigger_recall}</td>
      <td class="xs-numeric-column small-text">{extended_trigger_recall}</td>
      <td class="xs-numeric-column small-text">{baseline_trigger_recall}</td>
      <td class="xs-numeric-column small-text">{raw_activities_precision}</td>
      <td class="xs-numeric-column small-text">{extended_activities_precision}</td>
      <td class="xs-numeric-column small-text">{raw_activities_recall}</td>
      <td class="xs-numeric-column small-text">{extended_activities_recall}</td>
      <td class="xs-numeric-column small-text">{baseline_activities_recall}</td>
      <td class="small-text">{token_usage}<br><br> Raw Activities Returned: <span class="raw-act-count">{raw_activities_returned}</span><br><br>Raw Triggers Returned: {raw_triggers_returned} </td>
      <td class="small-text">Last relevant query index: {last_relevant_query_index} <br>Last Workflow index: {last_relevant_workflow_index}</td>
      <td class="small-text">{missed_activities}</td>
    </tr>
    <tr class="row-highlight secondary-row" style="display:none">
      <td colspan="17">
        <details>
          <summary class="small-section-header">Activity Details</summary>
          <table class ="details-table">
            <tr class="row-highlight secondary-fixed-header">
                <th>Proposed Activities</th>
                <th>Retrieved Activities</th>
            </tr>
            <tr class="row-highlight">
              <td>{proposed_activities}</td>
              <td>{retrieved_activities}</td>
            </tr>
          </table>
        </details>
        <details>
          <summary class="small-section-header">Trigger Details</summary>
          <table class ="details-table">
            <tr class="row-highlight secondary-fixed-header">
                <th>Proposed Triggers</th>
                <th>Retrieved Triggers</th>
            </tr>
            <tr class="row-highlight">
              <td>{proposed_triggers}</td>
              <td>{retrieved_triggers}</td>
            </tr>
          </table>
        </details>
        <p><span class="small-section-header">Plan: </span>{plan}</p>
        <p><span class="small-section-header">Ambiguity: </span>{ambiguity}</p>
        <p><span class="small-section-header">Query Score: </span>{query_score}</p>
        <p><span class="small-section-header">Ideally Expected But Inexistent Activity Type Names: </span>{ideally_expected_but_inexistent_activity_type_names}</p>
        <p><span class="small-section-header">Raw Activities Returned: </span>{raw_activities_returned}</p>
        <p><span class="small-section-header">Raw Triggers Returned: </span>{raw_triggers_returned}</p>
        <details>
          <summary><span class="small-section-header">Prompt:</span></summary>
          <pre class="script-column focused-script">{prompt}</pre>
        </details>
        <h3 class="small-section-header">Demonstrations</h3>
        {demonstrations}
      </td>
    </tr>
  sample_row_demonstrations_template: |-
    <details>
        <summary>{query}</summary>
        <div class="data-point"><span class="small-header">Plan:</span> {plan}</div>
        <div class="data-point"><span class="small-header">Ambiguities:</span> {ambiguities}</div>
        <div class="data-point"><span class="small-header">Score:</span> {score}</div>
        <div class="data-point"><span class="small-header">Used Activities</span></div>
        <table class ="details-table">
            <tr class="row-highlight secondary-fixed-header">
                <th>Used Activities</th>
                <th>Used Triggers</th>
            </tr>
            <tr class="row-highlight">
              <td>{used_activities}</td>
              <td>{used_triggers}</td>
            </tr>
        </table>
    </details>
  averages_row_template: |-
    <tr class="emphasis-row">
      <td>Averages</td>
      <td class="xs-numeric-column">{elapsed_time}</td>
      <td class="xs-numeric-column">{ambiguities_similarity}</td>
      <td class="xs-numeric-column">{plan_similarity}</td>
      <td class="xs-numeric-column">{score_precision}</td>
      <td class="xs-numeric-column">{trigger_precision}</td>
      <td class="xs-numeric-column">{trigger_recall}</td>
      <td class="xs-numeric-column">{extended_trigger_recall}</td>
      <td class="xs-numeric-column">{baseline_trigger_recall}</td>
      <td class="xs-numeric-column">{activities_precision}</td>
      <td class="xs-numeric-column">{extended_activities_precision}</td>
      <td class="xs-numeric-column">{activities_recall}</td>
      <td class="xs-numeric-column">{extended_activities_recall}</td>
      <td class="xs-numeric-column">{baseline_activities_recall}</td>
      <td class="xs-numeric-column">N/A</td>
      <td class="xs-numeric-column">N/A</td>
      <td class="xs-numeric-column">N/A</td>
    </tr>
  general_info_template: |-
      </table>
      <h3 class="section-header">Activity Usage Data</h3>
      <div class="data-point"><span class="small-header">Total Missing From Proposal</span></div>
      <div class="data-point">{total_missing_from_proposal}</div>
      <table class="secondary-info-table">
        <tr class="fixed-header fixed-header">
            <th class="large-column">Activity Name</th>
            <th>Missing Count</th>
            <th>Missing From Proposal</th>
            <th>Correctly Identified Count</th>
            <th>Last Query Index</th>
            <th>Last Workflow Index</th>
        </tr>
        {missing_activities}
      </table>
      <h3 class="section-header">General Information</h3>
      <div class="data-point"><span class="small-header">Prompt Template</span></div>
      <div class="data-point">{prompt}</div>
      <div class="data-point"><span class="small-header">Model Details Activities</span></div>
      <div class="data-point">{model}</div>
      <div class="data-point"><span class="small-header">Demonstration Ranking Details</span></div>
      <div class="data-point">{demonstration_ranking}</div>
      <div class="data-point">{demonstration_filtering}</div>
      <div class="data-point">{proposed_activities_selection}</div>
  missing_activities_row_template: |-
    <tr class="row-highlight">
      <td class="title-cell sample-details-button">{activity_Name}</td>
      <td>{missing_count}</td>
      <td>{missing_from_proposal}</td>
      <td>{correctly_identified_count}</td>
      <td>{query_index}</td>
      <td>{workflow_index}</td>
    </tr>
    <tr class="row-highlight secondary-row" style="display:none">
      <td colspan="6">
        <table class ="details-table">
          <tr class="row-highlight secondary-fixed-header">
              <th>Query</th>
              <th>Matched</th>
              <th>Query Index</th>
              <th>Workflow Index</th>
          </tr>
          {index_row_data}
        </table>
      </td>
    </tr>
  index_row_template: |-
    <tr class="row-highlight">
      <td>{query}</td>
      <td>{matched}</td>
      <td>{query_index}</td>
      <td>{workflow_index}</td>
    </tr>
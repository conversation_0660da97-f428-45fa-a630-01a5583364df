import pytest

from services.studio._text_to_workflow.api_workflow.services.api_wf_activity_retrieval_service import (
    APIActivityRetrievalService,
)
from services.studio._text_to_workflow.common.api_activity_retriever import (
    APIActivitiesRetriever,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import (
    ConnectionEmbeddingsRetriever,
)


@pytest.fixture
def embedding_model():
    """Fixture to provide an embedding model."""
    return ModelManager().get_embeddings_model("activities_embedding_model")


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "query, mode, expected_activity",
    [
        (
            "Create a workflow to get time off requests from BambooHR for 'EmployeeId' between 'StartDate' and 'EndDate'",
            "workflow",
            "UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRGet_Time_off_Requests",
        ),
        (
            "Get the cheapest flight from Bucharest to London and send it to a WhatsApp number via Twilio",
            "workflow",
            "UiPath.IntegrationService.Activities.Runtime.Activities.TwilioSend_WhatsApp_Message",
        ),
    ],
)
async def test_get_relevant_activities(
    embedding_model,
    query,
    mode,
    expected_activity,
    api_activities_retriever: APIActivitiesRetriever,
):
    """Test that generate_relevant_activities returns appropriate activities."""
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(api_activities_retriever, embedding_model)
    api_activities_retrieval_service = APIActivityRetrievalService(api_activities_retriever, connection_embeddings_retriever)

    # Get relevant activities
    activities_retrieval_result = await api_activities_retrieval_service.generate_relevant_activities(
        query=query,
        workflow=None,
        connections=[],
        eval_mode=False,
    )

    assert len(activities_retrieval_result.retrieved_activities) > 0
    assert len(activities_retrieval_result.retrieved_activities) <= 200

    found = False
    for activity in activities_retrieval_result.retrieved_activities:
        if expected_activity in activity:
            found = True
            break

    assert found


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "query, mode, http_generic_activity",
    [
        (
            "Create a workflow to get time off requests from BambooHR for 'EmployeeId' between 'StartDate' and 'EndDate'",
            "workflow",
            "UiPath.IntegrationService.Activities.Runtime.Activities.BambooHR_HTTP_Request",
        )
    ],
)
async def test_get_http_generic_activities(
    embedding_model,
    query,
    mode,
    http_generic_activity,
    api_activities_retriever: APIActivitiesRetriever,
):
    """Test that generate_relevant_activities returns appropriate activities."""
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(api_activities_retriever, embedding_model)
    api_activities_retrieval_service = APIActivityRetrievalService(api_activities_retriever, connection_embeddings_retriever)

    # Get relevant activities
    activities_retrieval_result = await api_activities_retrieval_service.generate_relevant_activities(
        query=query,
        workflow=None,
        connections=[],
        eval_mode=False,
    )

    assert len(activities_retrieval_result.retrieved_activities) > 0
    assert len(activities_retrieval_result.retrieved_activities) <= 200

    found = False
    for activity in activities_retrieval_result.retrieved_activities:
        if http_generic_activity in activity:
            found = True
            break

    assert found


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "query, mode, http_generic_activity",
    argvalues=[
        (
            "Create a workflow to make a dynamic http request to Salesforce",
            "workflow",
            "UiPath.IntegrationService.Activities.Runtime.Activities.Salesforce_HTTP_Request",
        )
    ],
)
async def test_get_http_generic_activities_in_proposed_activities(
    embedding_model,
    query,
    mode,
    http_generic_activity,
    api_activities_retriever: APIActivitiesRetriever,
):
    """Test that generate_relevant_activities returns appropriate activities."""
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(api_activities_retriever, embedding_model)
    api_activities_retrieval_service = APIActivityRetrievalService(api_activities_retriever, connection_embeddings_retriever)

    # Get relevant activities
    activities_retrieval_result = await api_activities_retrieval_service.generate_relevant_activities(
        query=query,
        workflow=None,
        connections=[],
        eval_mode=False,
    )

    found = False
    for activities in activities_retrieval_result.generation_details.query_proposal_activities:
        for activity in activities:
            if http_generic_activity == activity.get("type_full_name", None):
                found = True
                break

    assert found

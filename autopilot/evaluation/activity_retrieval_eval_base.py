import html
import json
import pathlib
import typing as t
from abc import abstractmethod

import numpy as np

from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflowDataPoint
from services.studio._text_to_workflow.common.schema import PlanStep
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import workflow_utils
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.evaluation.eval_base import BaseEvalService
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.activity_retrieval import (
    ActivityRetrievalEval,
    ActivityUsageDetails,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ProposedActivity, WfGenDataPointV2


class ActivityRetrievalEvalServiceBase(BaseEvalService):
    def __init__(
        self,
        embedding_model: EmbeddingModel,
        output_file_prefix: str,
        activity_retrieval_config: dict,
        cache_eval_results: bool = False,  # caching these results might be useful, they can be used for an eval in the draft generation
        batch_size: int = 4,
        retry_count: int = 3,
    ):
        super().__init__(output_file_prefix, batch_size, retry_count)
        self.embedding_model = embedding_model

        eval_config_path = (
            (pathlib.Path(__file__).parent.parent).absolute()
            / "services"
            / "studio"
            / "_text_to_workflow"
            / "workflow_generation"
            / "config"
            / "eval_templates.yaml"
        )
        eval_config = yaml_load(eval_config_path)
        self.common_templates = eval_config["common"]
        self.activity_retrieval_templates = eval_config["activity_retrieval"]

        self.cache_eval_results = cache_eval_results
        self.activity_retrieval_config = activity_retrieval_config

    def _embed_workflow_plan(self, plan: str) -> list[PlanStep]:
        steps = workflow_utils.get_display_names(plan)
        step_embeddings = self.embedding_model.encode_batch(steps, batch_size=64, instruction_set="icl", instruction_type="query")
        return [PlanStep(text=step, embedding=step_embedding, triggers=[], activities=[]) for step, step_embedding in zip(steps, step_embeddings, strict=True)]

    @staticmethod
    def _get_first_match_index(activities: list[list[ProposedActivity]], type_full_name: str) -> int | None:
        for i, activity_list in enumerate(activities):
            for activity in activity_list:
                if activity["type_full_name"] == type_full_name:
                    return i

        return None

    @abstractmethod
    def serialize_demonstration(self, demonstration) -> str:
        raise NotImplementedError()

    def _get_sample_query(self, sample: WfGenDataPointV2 | ApiWorkflowDataPoint) -> str:
        if isinstance(sample, ApiWorkflowDataPoint):
            return sample.query
        else:
            return sample["query"]

    @staticmethod
    def _exclude_nones(lst: list):
        return [*filter(lambda x: x is not None, lst)]

    def _print_samples(self, log_file: pathlib.Path):
        used_activities_counter: dict[str, ActivityUsageDetails] = {}

        with log_file.open("w") as f:
            f.write(self.common_templates["html_template_start"])
            f.write(self.activity_retrieval_templates["body_template_start"])

            eval_results: list[ActivityRetrievalEval] = t.cast(list[ActivityRetrievalEval], self.results)

            for eval in eval_results:
                missed_activities = set()

                proposed_activities_names = [act["type_full_name"] for act in eval.proposed_activities + eval.proposed_triggers]

                # for each used activity, we count how many times it is correctly found and/or missed
                for activity, activity_index in eval.input_activity_indexes.items():
                    if activity not in used_activities_counter:
                        used_activities_counter[activity] = ActivityUsageDetails(activity)
                    used_activities_counter[activity].indexes.append(activity_index)

                    if activity not in eval.retrieved_activities + (eval.retrieved_triggers or []):
                        used_activities_counter[activity].missed_count += 1
                        missed_activities.add(activity)
                    else:
                        used_activities_counter[activity].correctly_identified_count += 1

                    if activity not in proposed_activities_names:
                        used_activities_counter[activity].missing_from_proposal += 1

                serialized_demonstrations: list[str] = [self.serialize_demonstration(demonstration) for demonstration in eval.demonstrations]

                query_indexes = [index.query_index for index in eval.input_activity_indexes.values() if index.query_index is not None]
                last_relevant_query_index = max(query_indexes, default=-1)
                wf_indexes = [index.workflow_index for index in eval.input_activity_indexes.values() if index.workflow_index is not None]
                last_relevant_workflow_index = max(wf_indexes, default=-1)

                f.write(
                    self.activity_retrieval_templates["sample_row_template"].format(
                        query=self._get_sample_query(eval.sample),
                        elapsed_time=eval.elapsed_time,
                        raw_activities_precision=eval.raw_activities_precision,
                        extended_activities_precision=eval.extended_activities_precision,
                        raw_activities_recall=eval.raw_activities_recall,
                        extended_trigger_recall=eval.extended_trigger_recall,
                        extended_activities_recall=eval.extended_activities_recall,
                        extended_trigger_precision=eval.extended_trigger_precision,
                        raw_trigger_recall=eval.raw_trigger_recall,
                        baseline_trigger_recall=eval.baseline_trigger_recall,
                        baseline_activities_recall=eval.baseline_activities_recall,
                        score_precision=eval.score_precision,
                        ambiguities_similarity=eval.ambiguities_similarity,
                        plan_similarity=eval.plan_similarity,
                        missed_activities="<br>".join(missed_activities),
                        proposed_activities="<br>".join([act["type_full_name"] for act in eval.proposed_activities]),
                        proposed_triggers="<br>".join([act["type_full_name"] for act in eval.proposed_triggers]),
                        retrieved_activities="<br>".join(eval.retrieved_activities),
                        retrieved_triggers="<br>".join(eval.retrieved_triggers),
                        token_usage=json.dumps(eval.token_usage.to_json(), indent=4),
                        last_relevant_query_index=last_relevant_query_index,
                        last_relevant_workflow_index=last_relevant_workflow_index,
                        demonstrations="\n".join(serialized_demonstrations),
                        prompt=eval.prompt,
                        plan=eval.generation.plan.replace("\n", "<br>") if eval.generation.plan else "",
                        ideally_expected_but_inexistent_activity_type_names=(
                            (eval.generation.inexistentActivities or []) + (eval.generation.inexistentTriggers or [])
                        ),
                        ambiguity=eval.generation.ambiguities,
                        query_score=eval.generation.score,
                        raw_activities_returned=len(eval.generation.activities),
                        raw_triggers_returned=len(eval.generation.triggers or []),
                    )
                )

            # build row with averages
            total_trigger_precision = self._exclude_nones([eval.extended_trigger_precision for eval in eval_results])
            total_trigger_recall = self._exclude_nones([eval.raw_trigger_recall for eval in eval_results])
            total_baseline_trigger_recall = self._exclude_nones([eval.baseline_trigger_recall for eval in eval_results])
            total_extended_trigger_recall = self._exclude_nones([eval.extended_trigger_recall for eval in eval_results])

            f.write(
                self.activity_retrieval_templates["averages_row_template"].format(
                    elapsed_time=np.mean([eval.elapsed_time for eval in eval_results]),
                    activities_precision=np.mean([eval.raw_activities_precision for eval in eval_results if eval.raw_activities_precision is not None]),
                    extended_activities_precision=np.mean(
                        [eval.extended_activities_precision for eval in eval_results if eval.extended_activities_precision is not None]
                    ),
                    activities_recall=np.mean([eval.raw_activities_recall for eval in eval_results if eval.raw_activities_recall is not None]),
                    extended_activities_recall=np.mean(
                        [eval.extended_activities_recall for eval in eval_results if eval.extended_activities_recall is not None]
                    ),
                    trigger_precision=np.mean(total_trigger_precision) if len(total_trigger_precision) > 0 else 1,
                    trigger_recall=np.mean(total_trigger_recall) if len(total_trigger_recall) > 0 else 1,
                    baseline_trigger_recall=np.mean(total_baseline_trigger_recall) if len(total_baseline_trigger_recall) > 0 else 1,
                    extended_trigger_recall=np.mean(total_extended_trigger_recall) if len(total_extended_trigger_recall) > 0 else 1,
                    baseline_activities_recall=np.mean(
                        [eval.baseline_activities_recall for eval in eval_results if eval.baseline_activities_recall is not None]
                    ),
                    score_precision=np.mean([eval.score_precision for eval in eval_results]),
                    ambiguities_similarity=np.mean([eval.ambiguities_similarity for eval in eval_results]),
                    plan_similarity=np.mean([eval.plan_similarity for eval in eval_results]),
                )
            )

            # show first the activities that were missed the most
            sorted_activities = sorted(used_activities_counter.values(), key=lambda item: item.missed_count, reverse=True)

            used_activity_formatted_rows = []
            total_missing_from_proposal = 0
            for activity_details in sorted_activities:
                # for each activity, we show each query where it was used and each corresponding index
                aggregated_dataset_details = "\n".join(
                    [
                        self.activity_retrieval_templates["index_row_template"].format(
                            query=index.query, matched=index.matched, query_index=index.query_index, workflow_index=index.workflow_index
                        )
                        for index in activity_details.indexes
                    ]
                )

                # track the last relevant index for the current activity. this helps understand how to finetune the list of candidates
                last_relevant_query_index = max([index.query_index for index in activity_details.indexes if index.query_index is not None], default=-1)
                last_relevant_workflow_index = max([index.workflow_index for index in activity_details.indexes if index.workflow_index is not None], default=-1)
                total_missing_from_proposal += activity_details.missing_from_proposal

                used_activity_formatted_rows.append(
                    self.activity_retrieval_templates["missing_activities_row_template"].format(
                        activity_Name=activity_details.type_full_name,
                        missing_count=activity_details.missed_count,
                        missing_from_proposal=activity_details.missing_from_proposal,
                        correctly_identified_count=activity_details.correctly_identified_count,
                        query_index=last_relevant_query_index,
                        workflow_index=last_relevant_workflow_index,
                        index_row_data=aggregated_dataset_details,
                    )
                )

            model_options = eval_results[0].generation_settings.model_options["retrieval"]  # should be same for all
            model_settings = ModelManager().get_llm_config(t.cast(str, model_options["model_name"])) if "model_name" in model_options else {}
            f.write(
                self.activity_retrieval_templates["general_info_template"].format(
                    prompt=html.escape(self.activity_retrieval_config["prompt"]["system_msg"]).replace("\n", "<br>"),
                    model=json.dumps(model_settings, indent=4),
                    demonstration_ranking=json.dumps(self.activity_retrieval_config["demonstration_ranking"], indent=4),
                    demonstration_filtering=json.dumps(self.activity_retrieval_config["prompt"]["demonstration_filtering"], indent=4),
                    proposed_activities_selection=json.dumps(self.activity_retrieval_config["prompt"]["proposed_activities_selection"], indent=4),
                    missing_activities="\n".join(used_activity_formatted_rows),
                    total_missing_from_proposal=total_missing_from_proposal,
                )
            )

            # Add parsing exceptions section
            if hasattr(self, "parsing_errors") and self.parsing_errors:
                exception_rows = []
                for error in self.parsing_errors:
                    exception_rows.append(
                        self.common_templates["parsing_exception_row_template"].format(
                            query=error["sample"]["query"] if isinstance(error["sample"], dict) and "query" in error["sample"] else "Unknown query",
                            output=html.escape(error["output"]),
                        )
                    )
                f.write(
                    self.common_templates["parsing_exceptions_section_template"].format(
                        exception_rows="\n".join(exception_rows), exception_count=len(self.parsing_errors)
                    )
                )

            f.write(self.common_templates["html_template_end"])

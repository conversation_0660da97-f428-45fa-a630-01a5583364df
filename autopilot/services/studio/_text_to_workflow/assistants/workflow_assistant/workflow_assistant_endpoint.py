import copy
import traceback
from abc import ABC, abstractmethod
from typing import Generic, TypeVar, cast

import langchain.prompts
import langchain_core.messages

from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_router_task import (
    WorkflowRouterBaseTask,
    WorkflowRouterTask,
)
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import (
    RPAWorkflowAssistantRequest,
    WorkflowAssistantBaseRequest,
    WorkflowAssistantChangeResponse,
    WorkflowAssistantRouterResponse,
    WorkflowRouterException,
    WorkflowScenarioType,
)
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.schema import ActivityDefinition
from services.studio._text_to_workflow.common.walkers import ActivityTypeCollector, IntegrationServiceActivityReplacer
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.request_schema import ModelOptions
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.workflow_utils import load_workflow_instance, replace_blobs_with_hash, revert_blobs_from_hash
from services.studio._text_to_workflow.workflow_fix.workflow_fix_endpoint import TASK as WORKFLOW_FIX_TASK
from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WORKFLOW_FIX_MODEL_NAME
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.dataset import Supported_Excel_Packages, Supported_Mail_Packages
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import GenerationSettingsBuilder
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalService,
)

# Add the draft generation imports here at the top
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_draft_service import WorkflowGenerationDraftService
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_endpoint import TASK as WORKFLOW_GENERATION_TASK
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent

ERROR_MESSAGE = "I can't help with this request. Please ask about workflow automation or refine your query."

ReqT = TypeVar("ReqT", bound=WorkflowAssistantBaseRequest)

# Get logger instance
LOGGER = AppInsightsLogger()


class WorkflowAssistantBaseEndpoint(ABC, Generic[ReqT]):
    router_task: WorkflowRouterBaseTask

    @telemetry_utils.log_execution_time("workflow_assistant_endpoint.perform_assistant_request")
    async def perform_assistant_request(self, request: ReqT, message_emitter: MessageEmitter) -> WorkflowAssistantChangeResponse:
        response = await self._perform_assistant_request(request, message_emitter)
        if response.scenario == "error":
            raise WorkflowRouterException(response.message)

        if response.newWorkflowContent:
            await message_emitter.emit_debug_message(lambda r=response: "New Workflow:\n```yaml\n" + r.newWorkflowContent + "\n```")
        return response

    @telemetry_utils.log_execution_time("workflow_assistant_endpoint.perform_assistant_request")
    async def _perform_assistant_request(self, request: ReqT, message_emitter: MessageEmitter) -> WorkflowAssistantChangeResponse:
        # First, route the request
        try:
            router_response = await self.router_task.process(request, message_emitter)
        except Exception:
            if settings.IS_PROD:
                error_message = "An error occurred while processing the request. Please try again."
            else:
                error_message = f"An error occurred while processing the request. Debugging info:\n{traceback.format_exc()}"
            return WorkflowAssistantChangeResponse(
                scenario="error",
                newWorkflowContent=None,
                previousWorkflowContent=None,
                message=error_message,
            )

        # If the request is not valid, return a proper error response
        if not router_response.valid:
            return WorkflowAssistantChangeResponse(
                scenario="error",
                newWorkflowContent=None,
                previousWorkflowContent=None,
                message=ERROR_MESSAGE,
            )

        # If we need a follow-up, ask the question and return
        if router_response.followUp and router_response.question:
            return WorkflowAssistantChangeResponse(
                scenario="follow-up",
                newWorkflowContent=None,
                previousWorkflowContent=None,
                message=router_response.question,
            )

        # Process based on the scenario
        scenario = router_response.scenario
        await message_emitter.emit_debug_message(f"Processing scenario: {scenario}")

        return await self.handle_assistant_scenario(scenario, router_response, request, message_emitter)

    async def handle_assistant_scenario(
        self, scenario: WorkflowScenarioType, router_response: WorkflowAssistantRouterResponse, request: ReqT, message_emitter: MessageEmitter
    ) -> WorkflowAssistantChangeResponse:
        # Process the specific scenario
        if scenario == "edit-workflow":
            return await self.handle_edit_workflow(router_response, request, message_emitter)
        elif scenario == "analyze-workflow":
            return await self.handle_analyze_workflow(router_response, message_emitter)
        elif scenario == "generate-workflow":
            return await self.handle_generate_workflow(router_response, request, message_emitter)
        elif scenario == "rewrite-workflow":
            return await self.handle_rewrite_workflow(router_response, request, message_emitter)
        else:
            return WorkflowAssistantChangeResponse(
                scenario="error",
                newWorkflowContent=None,
                previousWorkflowContent=None,
                message=ERROR_MESSAGE,
            )

    async def handle_analyze_workflow(
        self, router_response: WorkflowAssistantRouterResponse, message_emitter: MessageEmitter
    ) -> WorkflowAssistantChangeResponse:
        """Handle the analyze-workflow scenario by using a direct LLM call"""
        await message_emitter.emit_message("I'll analyze your workflow and figure out what it does.")

        # Check if we have a current workflow reference in the router response
        if not router_response.workflowRefs:
            error_message = "No workflow references to analyze. Could you please provide a workflow reference?"
            return WorkflowAssistantChangeResponse(
                scenario="analyze-workflow",
                newWorkflowContent=None,
                previousWorkflowContent=None,
                message=error_message,
            )

        # Get the first workflow reference (which should be the one to analyze)
        target_workflow = router_response.workflowRefs[0]

        # Use a model from ModelManager for analysis
        model = ModelManager().get_llm_model("workflow_analyzer_model", ConsumingFeatureType.WORKFLOW_GENERATION)

        # Create a simple prompt for analysis
        system_template = """You are a workflow analysis expert. Analyze the provided workflow and provide insights. A workflow is a collection of activities that are executed in a specific order.
    Focus on:
    1. The overall purpose of the workflow
    2. The main components and their functions
    3. Any potential issues or improvements
    4. How different activities work together
    5. Maintainability of the workflow
    6. Exception handling and error management
    7. Any other relevant insights
    8. When describing activities, try using a friendly name of the activity instead of the technical name - eg: instead of "Sequence_1", use "First Sequence"

    Be concise but thorough in your analysis."""

        # Include additional instructions if available
        request_with_instructions = router_response.request
        if router_response.instructions:
            request_with_instructions = f"{router_response.request}\n\nAdditional instructions: {router_response.instructions}"

        user_template = """Please analyze this workflow:
    ```yaml
    {workflow}
    ```

    User request: {request}"""

        messages = [
            langchain_core.messages.SystemMessage(content=system_template),
            langchain_core.messages.HumanMessage(content=user_template.format(workflow=target_workflow.content, request=request_with_instructions)),
        ]

        prompt = langchain.prompts.ChatPromptTemplate.from_messages(messages)
        chain = prompt | model

        response = await chain.ainvoke({"workflow": target_workflow.content, "request": request_with_instructions})
        analysis = cast(str, response.content)

        return WorkflowAssistantChangeResponse(
            path=target_workflow.path,
            scenario="analyze-workflow",
            newWorkflowContent=None,
            previousWorkflowContent=None,
            message=analysis,
        )

    @abstractmethod
    async def handle_edit_workflow(
        self,
        router_response: WorkflowAssistantRouterResponse,
        request: ReqT,
        message_emitter: MessageEmitter,
        emit_message: bool = True,
    ) -> WorkflowAssistantChangeResponse:
        """Handle the edit-workflow scenario by using workflow draft generation in edit mode"""
        pass

    @abstractmethod
    async def handle_generate_workflow(
        self, router_response: WorkflowAssistantRouterResponse, request: ReqT, message_emitter: MessageEmitter
    ) -> WorkflowAssistantChangeResponse:
        """Handle the generate-workflow scenario by using workflow generation"""
        pass

    @abstractmethod
    async def handle_rewrite_workflow(
        self, router_response: WorkflowAssistantRouterResponse, request: ReqT, message_emitter: MessageEmitter
    ) -> WorkflowAssistantChangeResponse:
        """Handle the rewrite-workflow scenario"""
        pass


class WorkflowAssistantEndpoint(WorkflowAssistantBaseEndpoint[RPAWorkflowAssistantRequest]):
    def __init__(self):
        super().__init__()
        self.router_task = WorkflowRouterTask()
        self.draft_services_are_loaded = False

    async def _ensure_draft_services_are_loaded(self):
        """Ensure draft services are loaded"""
        # TODO: find a good way to split this eager + lazy loading (currently there's a global instance of api workflow which loads prematurely some states at init time, which is breaking)
        if not self.draft_services_are_loaded:
            self.activities_fetcher = ActivitiesRetriever()
            self.embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
            self.connection_embeddings_retriever = ConnectionEmbeddingsRetriever(self.activities_fetcher, self.embedding_model)
            self.activities_retrieval_service = WorkflowGenerationActivityRetrievalService(self.activities_fetcher, self.connection_embeddings_retriever)
            self.dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(self.activities_fetcher)
            self.postprocess_component = WorkflowGenerationPostProcessComponent(
                self.activities_fetcher, self.dynamic_activities_component, self.activities_retrieval_service.ignored_activities
            )
            self.prompt_builder_component = WorkflowGenerationPromptBuilderComponent(self.activities_fetcher)
            self.draft_service = WorkflowGenerationDraftService(self.activities_fetcher, self.postprocess_component, self.prompt_builder_component)
            await self.draft_service.init_and_load()
            self.draft_services_are_loaded = True

    async def handle_assistant_scenario(
        self,
        scenario: WorkflowScenarioType,
        router_response: WorkflowAssistantRouterResponse,
        request: RPAWorkflowAssistantRequest,
        message_emitter: MessageEmitter,
    ) -> WorkflowAssistantChangeResponse:
        # Process the specific scenario
        if scenario == "edit-workflow":
            return await self.handle_edit_workflow(router_response, request, message_emitter)
        elif scenario == "fix-workflow":
            return await self.handle_fix_workflow(router_response, request, message_emitter)
        elif scenario == "analyze-workflow":
            return await self.handle_analyze_workflow(router_response, message_emitter)
        elif scenario == "generate-workflow":
            return await self.handle_generate_workflow(router_response, request, message_emitter)
        elif scenario == "rewrite-workflow":
            return await self.handle_rewrite_workflow(router_response, request, message_emitter)
        else:
            return WorkflowAssistantChangeResponse(scenario="error", message=ERROR_MESSAGE, newWorkflowContent=None, previousWorkflowContent=None)

    async def handle_edit_workflow(
        self, router_response: WorkflowAssistantRouterResponse, request: RPAWorkflowAssistantRequest, message_emitter: MessageEmitter, emit_message: bool = True
    ) -> WorkflowAssistantChangeResponse:
        """Handle the edit-workflow scenario by using workflow draft generation in edit mode"""
        if emit_message:
            await message_emitter.emit_message("I'll edit your workflow based on your request.")

        # Check if we have a current workflow reference in the router response
        if not router_response.workflowRefs:
            error_message = "No workflow references to edit. Could you please provide a workflow reference?"
            return WorkflowAssistantChangeResponse(
                scenario="edit-workflow",
                newWorkflowContent="",
                previousWorkflowContent="",
                message=error_message,
            )

        # Ensure draft services are initialized
        await self._ensure_draft_services_are_loaded()

        # Get the first workflow reference (which should be the one to edit)
        target_workflow = router_response.workflowRefs[0]

        # Hack: Replace DAP activities with virtual activity names
        target_workflow_object = self.build_editable_workflow(request, target_workflow)
        target_workflow_lmyaml = target_workflow_object.lmyaml(include_ids=True)
        # Note: There is a blob replacement inside the workflow_generation run as well, but will not trigger as long as the hash length (currently 64) is smaller than the min_length of the blob replacement (currently 100)
        target_workflow_lmyaml, blob_replacement_mapping = replace_blobs_with_hash(target_workflow_lmyaml, use_b64_charset=True)

        # Create a modified query that makes it clear this is editing the current workflow
        # Include additional instructions if available
        user_prompt_additional_instructions = []
        if router_response.instructions:
            user_prompt_additional_instructions.append(f"Additional instructions: {router_response.instructions}")

        user_prompt_additional_instructions.append("Ensure that you add scopes if required.")
        user_prompt_additional_instructions.append("Only make changes that are necessary to fulfill the request.")

        # Add additional workflow references as context
        additional_workflows = []
        # Include other workflow references from workflowRefs (starting from index 1)
        if len(router_response.workflowRefs) > 1:
            for i, workflow in enumerate(router_response.workflowRefs[1:], 1):
                workflow_content, _ = replace_blobs_with_hash(workflow.content, use_b64_charset=True)
                additional_workflows.append(f"Reference Workflow {i} (DO NOT EDIT THIS - use only as reference):\n```yaml\n{workflow_content}```")

        # Append additional workflows to the query if available
        if additional_workflows:
            user_prompt_additional_instructions.append(
                "REFERENCE WORKFLOWS (DO NOT EDIT THESE - use only as reference material, inspiration, or templates as mentioned in the request):"
            )
            user_prompt_additional_instructions.extend(additional_workflows)

        # Determine if we should use a reasoning model based on score
        _model_options = await self._get_generate_model_options(router_response, message_emitter)
        # TODO: Should we hook model_options to the draft call somehow?

        # TODO: Should we still handle special testcase? "testcase" if target_workflow.path and "test" in target_workflow.path.lower() else "workflow"

        try:
            # Create workflow object and prepare for draft generation
            existing_workflow = Workflow("", "", target_workflow_object.to_dict())
            target_framework = request.projectDefinition.targetFramework

            # Use the modified query that was constructed above
            generation_settings = GenerationSettingsBuilder.build_generation_settings(
                router_response.request, target_framework, None, [], Supported_Excel_Packages, Supported_Mail_Packages
            )

            # Get relevant activities and generate draft
            activity_retrieval_result = await self.activities_retrieval_service.generate_relevant_activities(
                router_response.request, existing_workflow, request.availableEntities.connections, "edit", target_framework, generation_settings, True
            )

            draft_result = await self.draft_service.generate_workflow_draft(
                router_response.request,
                existing_workflow,
                request.availableEntities.connections,
                request.currentWorkflowDesignerState.availableVariables,
                request.projectDefinition.objects,
                "edit",
                target_framework,
                activity_retrieval_result,
                "en",
                additional_type_definitions=request.currentWorkflowDesignerState.availableAdditionalTypeDefinitions,
                user_prompt_additional_instructions="\n\n".join(user_prompt_additional_instructions),
            )

            # Postprocess the generated workflow
            raw_wf = draft_result.result_workflow_details.workflow_generation["workflow_valid"]
            gen_wf = await self.postprocess_component.postprocess_generation(
                model=None,
                workflow_raw_yaml=raw_wf,
                query=router_response.request,
                jit_types=draft_result.jit_types,
                activities_config_map=draft_result.activities_config_map,
                connections_by_key={conn["connector"]: conn for conn in request.availableEntities.connections if "connector" in conn},
                target_framework=target_framework,
                objects=[],
                activities_and_triggers=draft_result.activity_defs + draft_result.trigger_defs,
                variables=[],
                localization="en",
                allow_triggers=True,
                allow_assign_activity_on_uia_expansion=True,
                consuming_feature_type=ConsumingFeatureType.WORKFLOW_GENERATION,
            )

            # Extract the final workflow
            generated_workflow_content = gen_wf["workflow_valid"]
            generated_workflow_content = revert_blobs_from_hash(generated_workflow_content, blob_replacement_mapping)

            # Create and return the WorkflowAssistantChangeResponse with the same structure as before
            return WorkflowAssistantChangeResponse(
                path=target_workflow.path,
                scenario="edit-workflow",
                newWorkflowContent=generated_workflow_content,
                previousWorkflowContent=target_workflow.content,
                message="I've updated your workflow based on your request.",
                jitCommands=gen_wf["jitCommands"] if "jitCommands" in gen_wf else [],
                testCase=router_response.testCase,
            )

        except Exception as e:
            error_message = f"Failed to edit workflow: {str(e)}\n{traceback.format_exc()}"
            LOGGER.exception(error_message)
            await message_emitter.emit_debug_message(lambda: error_message)
            return WorkflowAssistantChangeResponse(
                scenario="edit-workflow",
                path=target_workflow.path,
                newWorkflowContent=None,
                previousWorkflowContent=None,
                message=f"I was unable to edit the workflow at path {target_workflow.path}. Please try again.",
            )

    def build_editable_workflow(self, request, target_workflow):
        target_workflow_object = load_workflow_instance("", target_workflow.content)

        collector = ActivityTypeCollector().walk(target_workflow_object)

        activity_defs: list[ActivityDefinition] = []
        existing_activity_types = set(collector.get_activity_types())

        for activity_type in existing_activity_types:
            activity_def = WORKFLOW_GENERATION_TASK.activities_retriever.get(activity_type, request.projectDefinition.targetFramework, None)
            if not activity_def:
                continue

            type_activities = collector.typed_activity_instances[activity_type]
            for activity in type_activities:
                curr_activity_def = copy.deepcopy(activity_def)

                if activity.is_dynamic:
                    curr_activity_def["uuid"] = activity.id
                    curr_activity_def["defaultConfiguration"] = curr_activity_def["activityConfiguration"]
                    curr_activity_def["activityIsConfigured"] = False
                    if activity.dap_is_configured:
                        # Override the type def only for configured activities,
                        # also keeping the default embedding config string.
                        curr_activity_def["activityConfiguration"] = activity.dap_config
                        curr_activity_def["activityIsConfigured"] = True
                        curr_activity_def["dynamicActivityDetails"] = activity.dynamic_activity_details

                activity_defs.append(curr_activity_def)

        dap_uuid_class_name: dict[str, str] = {
            type_def["uuid"]: type_def["fullClassName"] for type_def in activity_defs if type_def["activityTypeId"] and "uuid" in type_def and type_def["uuid"]
        }

        IntegrationServiceActivityReplacer(dap_uuid_class_name).process_workflow(target_workflow_object)
        return target_workflow_object

    async def handle_fix_workflow(
        self, router_response: WorkflowAssistantRouterResponse, request: RPAWorkflowAssistantRequest, message_emitter: MessageEmitter
    ) -> WorkflowAssistantChangeResponse:
        """Handle the fix-workflow scenario by using workflow fix task"""
        await message_emitter.emit_message("I'll fix the issues in your workflow.")

        # Check if we have a current workflow reference in the router response
        if not router_response.workflowRefs:
            error_message = "No workflow references to fix. Could you please provide a workflow reference?"
            return WorkflowAssistantChangeResponse(
                scenario="fix-workflow",
                newWorkflowContent="",
                previousWorkflowContent="",
                message=error_message,
            )

        # Get the first workflow reference (which should be the one to fix)
        target_workflow = router_response.workflowRefs[0]

        # Extract error messages - if no errors present, use the user query as the error
        error_messages = []
        if target_workflow.metadata.errors and len(target_workflow.metadata.errors) > 0:
            error_messages = [error.message for error in target_workflow.metadata.errors]
        else:
            # Include additional instructions if available
            error_message = router_response.request
            if router_response.instructions:
                error_message = f"{router_response.request}\n\nAdditional instructions: {router_response.instructions}"
            error_messages = [error_message]

        # Determine if we should use a reasoning model based on score
        model_options = await self._get_fix_model_options(router_response, message_emitter)

        # Call workflow fix task
        result = await WORKFLOW_FIX_TASK.run_fix_workflow(
            existing_workflow=target_workflow.content,
            errors=error_messages,
            additional_type_defs=request.currentWorkflowDesignerState.availableAdditionalTypeDefinitions,
            connections=request.availableEntities.connections,
            target_framework=request.projectDefinition.targetFramework,
            model_options=model_options,
            message_emitter=message_emitter,
        )

        if result and "fixedWorkflow" in result and result["fixedWorkflow"]:
            explanation = f"{result.get('reasoning', '')}. I analyzed these issues and attempted to fix them."

            # Create and return the WorkflowAssistantChangeResponse
            return WorkflowAssistantChangeResponse(
                path=target_workflow.path,
                scenario="fix-workflow",
                newWorkflowContent=result["fixedWorkflow"],
                previousWorkflowContent=target_workflow.content,
                message=explanation,
            )
        else:
            reasoning = result.get("reasoning", "No specific details available.")
            explanation = f"I analyzed the issues but couldn't fix them automatically, however I can provide you with some guidance on what the issues represent. {reasoning}"
            return WorkflowAssistantChangeResponse(
                scenario="fix-workflow",
                newWorkflowContent="",
                previousWorkflowContent=target_workflow.content,
                message=explanation,
            )

    async def handle_rewrite_workflow(
        self, router_response: WorkflowAssistantRouterResponse, request: RPAWorkflowAssistantRequest, message_emitter: MessageEmitter
    ) -> WorkflowAssistantChangeResponse:
        # TO DO: add full rewrite support for the RPA WF Draft Service - for now, we use the edit workflow endpoint
        return await self.handle_edit_workflow(router_response, request, message_emitter)

    async def handle_generate_workflow(
        self, router_response: WorkflowAssistantRouterResponse, request: RPAWorkflowAssistantRequest, message_emitter: MessageEmitter
    ) -> WorkflowAssistantChangeResponse:
        """Handle the generate-workflow scenario by using workflow generation task"""
        await message_emitter.emit_message("I'll generate a new workflow based on your request.")

        # Determine if we should use a reasoning model based on score
        model_options = await self._get_generate_model_options(router_response, message_emitter)

        # Include additional instructions if available
        query = router_response.request
        if router_response.testCase:
            query = f"{query}\n\nEnsure to add a verification to the test case using the verification activities."

        if router_response.instructions:
            query = f"{router_response.request}\n\nAdditional instructions: {router_response.instructions}"

        # Call workflow generation task
        # TODO: Enforce adding argument support for the generated workflow
        result = await WORKFLOW_GENERATION_TASK.generate_workflow(
            query=query,
            connections=request.availableEntities.connections,
            target_framework=request.projectDefinition.targetFramework,
            model_options=model_options,
            message_emitter=message_emitter,
            mode="testcase" if router_response.testCase else "workflow",
        )

        if result and "workflow_result" in result and "workflow_valid" in result["workflow_result"]:
            # For a new workflow, we use an empty string as the previous content
            return WorkflowAssistantChangeResponse(
                path=router_response.path,  # Use the path from router_response for new workflows
                scenario="generate-workflow",
                newWorkflowContent=result["workflow_result"]["workflow_valid"],
                previousWorkflowContent="",
                message="I've created a new workflow based on your request.",
                jitCommands=result["workflow_result"]["jitCommands"] if "jitCommands" in result["workflow_result"] else [],
                testCase=router_response.testCase,
            )
        else:
            error_message = "I was unable to generate a workflow. Please try again."
            return WorkflowAssistantChangeResponse(
                scenario="generate-workflow",
                path=router_response.path,
                newWorkflowContent=None,
                previousWorkflowContent=None,
                message=error_message,
            )

    async def _get_generate_model_options(self, router_response: WorkflowAssistantRouterResponse, message_emitter: MessageEmitter) -> dict[str, ModelOptions]:
        # TODO: For complexity == 3, we should use the large generation model after adding draft mode
        model_options: dict[str, ModelOptions] = {
            "retrieval": {"model_name": "activity_retrieval_reasoning_model" if router_response.score >= 5 else "activity_retrieval_gemini_model"},
            "generation": {"model_name": "workflow_generation_reasoning_model" if router_response.score >= 5 else "workflow_generation_gemini_model"},
        }
        await message_emitter.emit_debug_message(f"Using model options: {model_options}")

        if router_response.score > 4:
            await message_emitter.emit_message("Thinking longer about your request...")

        return model_options

    async def _get_fix_model_options(self, router_response: WorkflowAssistantRouterResponse, message_emitter: MessageEmitter) -> ModelOptions:
        model_options: ModelOptions = {"model_name": WORKFLOW_FIX_MODEL_NAME if router_response.score < 4 else "workflow_fix_reasoning_model"}
        await message_emitter.emit_debug_message(f"Using model options: {model_options}")

        if router_response.score >= 4:
            await message_emitter.emit_message("Thinking longer about your request...")

        return model_options

import os
import pathlib
from typing import Callable

from services.studio._text_to_workflow.activity_config import activity_config_schema
from services.studio._text_to_workflow.common import schema

WF_GEN_V2_DATASET_NAME = "WfGenV2"
API_WF_DATASET_NAME = "APIWFs"
API_WF_METADATA_DIR = "metadata"
API_WF_EDIT_EVAL_DATASET_NAME = "APIWFsEditEval"
API_WF_EDIT_MAPPINGS_FILE = "APIWFs_EditSamplesCorrespondence.yaml"

# API WF suffixes used to identify workflow edit datapoints
API_WF_SINGLE_ACTIVITY_WF_SUFFIX = "first_activity_only"
API_WF_EMPTY_SCOPES_WF_SUFFIX = "empty_scopes"
API_WF_REPLACE_ACTIVITIES_SAMPLE_SUFFIX = "activities_replace"
API_WF_EDIT_WF_SUFFIXES = [API_WF_SINGLE_ACTIVITY_WF_SUFFIX, API_WF_EMPTY_SCOPES_WF_SUFFIX, API_WF_REPLACE_ACTIVITIES_SAMPLE_SUFFIX]


def resolve_path_decorator(func: Callable[[], pathlib.Path]) -> Callable[[], pathlib.Path]:
    def wrapper(*args, **kwargs) -> pathlib.Path:
        path = func(*args, **kwargs)
        return path.resolve()

    return wrapper


@resolve_path_decorator
def get_workdir_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parents[4] / ".data"


@resolve_path_decorator
def get_runs_path() -> pathlib.Path:
    return get_workdir_path() / "Runs"


@resolve_path_decorator
def get_retrievers_path() -> pathlib.Path:
    return get_workdir_path() / "Retrievers"


@resolve_path_decorator
def get_partial_eval_results_path() -> pathlib.Path:
    return get_workdir_path() / "Eval.Results"


@resolve_path_decorator
def get_autopilot_samples_dataset_path() -> pathlib.Path:
    return get_workdir_path() / "Autopilot.Samples" / "Dataset"


def get_api_wf_edit_mappings_file_path() -> pathlib.Path:
    """
    Contains the mappings between the original and the edited API WF samples, along with the corresponding query.
    """
    return get_autopilot_samples_dataset_path() / API_WF_EDIT_MAPPINGS_FILE


def get_dataset_connections_cache_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "Connections" / "connections_cache.json"


def get_dataset_connections_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "Connections" / "connections.yaml"


def get_dataset_object_repository_cache_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "Objects" / "object_repository_cache.json"


def get_downloads_dataset_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "Downloads"


def get_converted_dataset_path(target_framework: schema.TargetFramework | None) -> pathlib.Path:
    path = get_autopilot_samples_dataset_path() / "Converted"
    if target_framework is not None:
        path = path / target_framework
    return path


def get_autopilot_samples_feature_dataset_path(name: str, target_framework: schema.TargetFramework | None, subset: str | None) -> pathlib.Path:
    path = get_autopilot_samples_dataset_path() / name
    if target_framework is not None:
        path = path / target_framework
    if subset is not None:
        if target_framework is None:
            raise ValueError("target_framework must be specified if subset is specified")
        path = path / subset
    return path


def get_testdata_generation_dataset_path(type_of_generation: str, type_of_demo: str) -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "TestdataGeneration" / type_of_generation / type_of_demo


def get_code_generation_dataset_path(type_of_generation: str, type_of_demo: str) -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "CodeGeneration" / "train" / type_of_generation / type_of_demo


def get_fix_code_dataset_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "FixCode" / "train"


def get_agent_evaluation_dataset_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "AgentEvaluation" / "train" / "demos"


def get_workflow_generation_dataset_path(target_framework: schema.TargetFramework | None = None, subset: str | None = None) -> pathlib.Path:
    return get_autopilot_samples_feature_dataset_path("WorkflowGeneration", target_framework, subset)


def get_wf_gen_activity_retriever_dataset_path(target_framework: schema.TargetFramework | None = None, subset: str | None = None) -> pathlib.Path:
    return get_autopilot_samples_feature_dataset_path(WF_GEN_V2_DATASET_NAME, target_framework, subset)


def get_api_workflow_dataset_root_path() -> pathlib.Path:
    """
    Root folder for all the API workflow samples. Contains both JS and JQ datapoints.
    """
    return get_autopilot_samples_dataset_path() / API_WF_DATASET_NAME


def get_api_workflow_dataset_metadata_path() -> pathlib.Path:
    """
    Root folder for all the API workflow metadata. Since these files can be large, we don't want to include them in the main datapoint file.
    """
    return get_autopilot_samples_dataset_path() / API_WF_DATASET_NAME / API_WF_METADATA_DIR


def get_api_workflow_edit_dataset_root_path() -> pathlib.Path:
    """
    Root folder for all the API workflow edit samples. Contains both JS and JQ datapoints.
    """
    return get_autopilot_samples_dataset_path() / API_WF_EDIT_EVAL_DATASET_NAME


def get_api_workflow_edit_dataset_path() -> pathlib.Path:
    # TODO: add support for other languages, too. For now we only support JavaScript.
    return get_api_workflow_edit_dataset_root_path() / "JS"


def get_api_workflow_static_retriever_dataset_path() -> pathlib.Path:
    """
    Contains the static demonstrations for the API workflow retriever. These should be used for every generation
    """
    # TODO: add support for other languages, too. For now we only support JavaScript.
    return get_api_workflow_dataset_root_path() / "Static" / "JS"


def get_api_workflow_retriever_dataset_path() -> pathlib.Path:
    # TODO: add support for other languages, too. For now we only support JavaScript.
    return get_api_workflow_dataset_root_path() / "JS"


def get_workflow_fix_dataset_path(subset: str | None = None) -> pathlib.Path:
    path = get_autopilot_samples_dataset_path() / "FixActivities"
    if subset is not None:
        path = path / subset
    return path


def get_sequence_generation_dataset_path(target_framework: schema.TargetFramework | None = None, subset: str | None = None) -> pathlib.Path:
    return get_autopilot_samples_feature_dataset_path("SequenceGeneration", target_framework, subset)


def get_workflow_generation_metadata_path(target_framework: schema.TargetFramework) -> pathlib.Path:
    return get_workflow_generation_dataset_path(target_framework) / "metadata.yaml"


def get_sequence_generation_metadata_path(target_framework: schema.TargetFramework) -> pathlib.Path:
    return get_sequence_generation_dataset_path(target_framework) / "metadata.yaml"


def get_semantic_diff_text_dataset_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "SemanticDiffText"


def get_activity_config_dataset_path(
    type: activity_config_schema.DatasetType,
    target_framework: schema.TargetFramework | None = None,
    subset: str | None = None,
) -> pathlib.Path:
    return get_autopilot_samples_feature_dataset_path(f"ActivityConfiguration/{type}", target_framework, subset)


def get_predict_expression_dataset_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "PredictExpression"


def get_workflow_summarization_dataset_path(target_framework: schema.TargetFramework | None = None, subset: str | None = None) -> pathlib.Path:
    return get_autopilot_samples_feature_dataset_path("WorkflowSummarization", target_framework, subset)


def get_orchestrator_production_data_path() -> pathlib.Path:
    return get_workdir_path() / "OrchestratorProductionData"


def get_orchestrator_production_metadata_path() -> pathlib.Path:
    return get_workdir_path() / "OrchestratorProductionData" / "Metadata"


def get_orchestrator_production_rawdata_path() -> pathlib.Path:
    return get_workdir_path() / "OrchestratorProductionData" / "Raw"


def get_orchestrator_production_converted_path() -> pathlib.Path:
    return get_workdir_path() / "OrchestratorProductionData" / "Converted"


def get_logs_path() -> pathlib.Path:
    return get_workdir_path() / "Logs"


def get_activity_config_retriever_path() -> pathlib.Path:
    if path := os.environ.get("ACTIVITY_CONFIG_RETRIEVER_PATH"):
        return pathlib.Path(path)
    retriever_name = os.environ.get("ACTIVITY_CONFIG_RETRIEVER_NAME", "ActivityConfigRetriever")
    return get_retrievers_path() / retriever_name


def get_activity_config_runs_path() -> pathlib.Path:
    return get_runs_path() / "ActivityConfig"


def get_activity_retriever_path() -> pathlib.Path:
    return get_retrievers_path() / "ActivityRetriever"


def get_api_activity_retriever_path() -> pathlib.Path:
    return get_retrievers_path() / "ApiActivityRetriever"


def get_workflow_generation_retriever_path() -> pathlib.Path:
    return get_retrievers_path() / "WorkflowGenerationRetriever"


def get_wf_gen_activity_retriever_path() -> pathlib.Path:
    return get_retrievers_path() / WF_GEN_V2_DATASET_NAME


def get_api_workflow_demonstrations_retriever_path() -> pathlib.Path:
    return get_retrievers_path() / "ApiWorkflowDemonstrationsRetriever"


def get_workflow_fix_retriever_path() -> pathlib.Path:
    return get_retrievers_path() / "WorkflowFixRetriever"


def get_sequence_generation_retriever_path() -> pathlib.Path:
    return get_retrievers_path() / "SequenceGenerationRetriever"


def get_workflow_summarization_retriever_path() -> pathlib.Path:
    if path := os.environ.get("WORKFLOW_SUMMARIZATION_RETRIEVER_PATH"):
        return pathlib.Path(path)
    retriever_name = os.environ.get("WORKFLOW_SUMMARIZATION_RETRIEVER_NAME", "WorkflowSummarizationRetriever")
    return get_retrievers_path() / retriever_name


def get_code_generation_retriever_path(target_framework: schema.TargetFramework | None) -> pathlib.Path:
    if path := os.environ.get("CODE_GENERATION_RETRIEVER_PATH"):
        return pathlib.Path(path)
    retriever_name = os.environ.get("CODE_GENERATION_RETRIEVER_NAME", "CodeGenerationRetriever")
    if target_framework is None:
        return get_retrievers_path() / retriever_name
    return get_retrievers_path() / retriever_name / target_framework


def get_connection_embeddings_retriever_path() -> pathlib.Path:
    if path := os.environ.get("CONNECTION_EMBEDDINGS_RETRIEVER_PATH"):
        return pathlib.Path(path)
    retriever_name = os.environ.get("CONNECTION_EMBEDDINGS_RETRIEVER_NAME", "ConnectionEmbeddingsRetriever")
    return get_retrievers_path() / retriever_name


def get_embeddings_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "Embeddings"


def get_models_path() -> pathlib.Path:
    return get_workdir_path() / "Models"


def get_typedefs_embeddings_path() -> pathlib.Path:
    return get_retrievers_path() / "TypeDefinitions"


def get_predict_expression_retrievers_path() -> pathlib.Path:
    return get_retrievers_path() / "PredictExpression"


def get_dotnet_path() -> pathlib.Path:
    return get_workdir_path() / "DotNet"


def get_cache_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent.parent / ".cache.langchain.db"


def get_bpmn_evaluation_dataset_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "BPMNEvaluation"


def get_meta_variables_prompt_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent.parent / "common" / "prompts" / "api_workflows_meta_variables.yaml"


def get_field_escaping_prompt_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent.parent / "common" / "prompts" / "field_escaping.yaml"


def get_activity_retrieval_prompt_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent.parent / "workflow_generation" / "config" / "activity_retrieval_prompt.yaml"


def get_api_workflows_dataset_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "WorkflowSummarization" / "Api"


def get_workflow_summarization_result_path() -> pathlib.Path:
    return get_autopilot_samples_dataset_path() / "WorkflowSummarization" / "Results" / "results.json"


def get_activity_summary_format_config_path() -> pathlib.Path:
    return pathlib.Path(__file__).resolve().parent.parent / "common" / "prompts" / "activity_summary_format_prompt.yaml"

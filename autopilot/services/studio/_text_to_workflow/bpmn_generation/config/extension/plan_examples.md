## **Examples of Correct Responses:**
**BPMN XML example shared by all the examples:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="40741f9c">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:extensionElements>
      <uipath:variables version="v1"/>
      <uipath:bindings version="v1">
        <uipath:binding id="bi4AhtGRd" name="releaseKey" type="string" default="E175C1D7-94EE-41A5-9322-5D55D56416B6" resource="process" resourceKey="E175C1D7-94EE-41A5-9322-5D55D56416B6" propertyAttribute="Key"/>
      </uipath:bindings>
    </bpmn:extensionElements>
    <bpmn:startEvent id="Event_start">
      <bpmn:extensionElements>
        <uipath:entryPointId value="00899dd7-2daf-4a4f-b13a-26847babe320"/>
      </bpmn:extensionElements>
      <bpmn:outgoing>edge___Event_start-Activity_f9kTJJ</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sendTask id="send_email" name="send email">
      <bpmn:incoming>edge___Event_start-Activity_f9kTJJ</bpmn:incoming>
      <bpmn:outgoing>edge___Activity_f9kTJJ-Activity_OiscgS</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:receiveTask id="receive_lead" name="find lead in salesforce">
      <bpmn:incoming>edge___Activity_f9kTJJ-Activity_OiscgS</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_OiscgS-Activity_r6QEdk</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:userTask id="user_finance" name="finance">
      <bpmn:incoming>edge_Activity_OiscgS-Activity_r6QEdk</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_r6QEdk-Activity_LpGyL5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="service_ticket" name="ticket">
      <bpmn:incoming>edge_Activity_r6QEdk-Activity_LpGyL5</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_r6QEdk-Activity_LpGyL6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:task id="task_report" name="Submit report">
      <bpmn:incoming>edge_Activity_r6QEdk-Activity_LpGyL6</bpmn:incoming>
      <bpmn:outgoing>edge_Activity_r6QEdk-Activity_report_email</bpmn:outgoing>
    </bpmn:task>
    <bpmn:serviceTask id="report_email" name="Send report email">
      <bpmn:incoming>edge_Activity_r6QEdk-Activity_report_email</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="edge___Event_start-Activity_f9kTJJ" sourceRef="Event_start" targetRef="send_email"/>
    <bpmn:sequenceFlow id="edge___Activity_f9kTJJ-Activity_OiscgS" sourceRef="send_email" targetRef="receive_lead"/>
    <bpmn:sequenceFlow id="edge_Activity_OiscgS-Activity_r6QEdk" sourceRef="receive_lead" targetRef="user_finance"/>
    <bpmn:sequenceFlow id="edge_Activity_r6QEdk-Activity_LpGyL5" sourceRef="user_finance" targetRef="service_ticket"/>
    <bpmn:sequenceFlow id="edge_Activity_r6QEdk-Activity_LpGyL6" sourceRef="service_ticket" targetRef="task_report"/>
    <bpmn:sequenceFlow id="edge_Activity_r6QEdk-Activity_report_email" sourceRef="task_report" targetRef="report_email"/>
  </bpmn:process>  
</bpmn:definitions>
```

### **Example 1:**
**User Query:** Change task ticket to a process

**Expected Output:**
```json
{
 "relevant_elements": [
   {
     "element": {
       "id": "service_ticket",
       "name": "ticket",
       "description": "A service task that handles ticket processing operations",
       "purpose": "Automates ticket-related processes in the workflow"
     },
     "extension_type": "rpa-workflow"
   }
 ],
 "title": "Convert Ticket Task to RPA Workflow Process",
 "explanation": "Found the 'ticket' service task (ID: service_ticket) in the BPMN diagram. Converting this task to an RPA workflow process will enable automated ticket processing operations. This transformation allows the task to execute automated workflows for ticket management, handling, routing, and processing without manual intervention."
}

```

### **Example 2:**
**User Query:** Add an agent to task ticket

**Expected Output (JSON):**
```json
{
 "relevant_elements": [
   {
     "element": {
       "id": "service_ticket",
       "name": "ticket",
       "description": "A service task that handles ticket processing operations",
       "purpose": "Processes and manages tickets within the workflow"
     },
     "extension_type": "agent"
   }
 ],
 "title": "Associate Agent to Ticket Task",
 "explanation": "Found the 'ticket' service task (ID: service_ticket) in the BPMN diagram. Adding an agent to this task will enable intelligent automation for ticket processing operations. The agent can handle ticket classification, routing, prioritization, and response generation automatically."
}
```

### **Example 3:**
**User Query:** Add human in the loop for task finance

**Expected Output:**
```json
{
 "relevant_elements": [
   {
     "element": {
       "id": "user_finance",
       "name": "finance",
       "description": "A user task that handles finance-related operations requiring human interaction",
       "purpose": "Manages financial processes that require human decision-making and oversight"
     },
     "extension_type": "action-app"
   }
 ],
 "title": "Associate Human-in-the-Loop Action App to Finance Task",
 "explanation": "Found the 'finance' user task (ID: user_finance) in the BPMN diagram. Adding a human-in-the-loop action app to this task will enable structured human interaction for finance-related operations. The action app will provide a user interface for financial decision-making, approvals, data entry, and other tasks that require human oversight in the finance workflow."
}
```

### **Example 4:**
**User Query:** Add activity for task ticket

**Expected Output:**
```json
{
 "relevant_elements": [
   {
     "element": {
       "id": "service_ticket",
       "name": "ticket",
       "description": "A service task that handles ticket processing operations",
       "purpose": "Processes and manages tickets within the workflow"
     },
     "extension_type": "rpa-workflow"
   }
 ],
 "title": "Associate RPA Workflow Activity to Ticket Task",
 "explanation": "Found the 'ticket' service task (ID: service_ticket) in the BPMN diagram. Adding an RPA workflow activity to this task will enable automated ticket processing operations. The workflow activity can handle ticket creation, routing, status updates, and other ticket management processes automatically."
}
```


### **Example 5:**
**User Query:** Add outlook connector event for task Submit report


**Expected Output:**
```json
{
  "relevant_elements": [
    {
      "element": {
        "id": "task_report",
        "name": "Submit report",
        "description": "A task that handles report submission operations",
        "purpose": "Manages the submission and processing of reports within the workflow"
      },
      "extension_type": "connector",
      "direction": "outgoing",
      "connectors": [
        {
          "connector": "uipath-microsoft-outlook365",
          "score": 1.0
        },
        {
          "connector": "uipath-microsoft-outlook365ews",
          "score": 0.8
        },
        {
          "connector": "uipath-mail-mail",
          "score": 0.6
        }
      ]
    }
  ],
  "title": "Associate Microsoft Outlook 365 Connector to Submit Report Task",
  "explanation": "Found the 'Submit report' task (ID: task_report) in the BPMN diagram. Adding the Microsoft Outlook 365 connector will enable automated email functionality for report submission processes. The latest Microsoft Outlook 365 connector provides comprehensive email automation capabilities including sending emails, managing calendars, creating events, and processing attachments. This connector supports both triggers and activities for complete email workflow automation, making it ideal for report submission scenarios where email notifications, confirmations, or report delivery are required."
}
```


### **Example 6:**
**User Query:** Add email connector event for task Submit report

**Expected Output:**
```json
{
 "relevant_elements": [
   {
     "element": {
       "id": "task_report",
       "name": "Submit report",
       "description": "A task that handles report submission operations",
       "purpose": "Manages the submission and processing of reports within the workflow"
     },
     "extension_type": "connector",
     "direction": "outgoing",
     "connectors": [
       {
         "connector": "uipath-microsoft-outlook365",
         "score": 1.0
       },
       {
         "connector": "uipath-google-gmail",
         "score": 0.9
       },
       {
         "connector": "uipath-mail-mail",
         "score": 0.8
       }
     ]
   }
 ],
 "title": "Associate Email Connector to Submit Report Task",
 "explanation": "Found the 'Submit report' task (ID: task_report) in the BPMN diagram. Adding an email connector will enable automated email functionality for report submission processes. The Microsoft Outlook 365 connector provides comprehensive email automation with event triggers, Gmail connector offers robust web-based email automation, and the Mail connector supports SMTP, IMAP, and POP3 protocols for versatile email operations including sending notifications, confirmations, or report delivery."
}
```

### **Example 7:**
**User Query:** Add xyz connector event for task Submit report

**Expected Output:**
```json
{
    "error": "There is no 'xyz connector'."
}
```

### **Example 8:**
**User Query:** can you suggest a queue for creating a queue item for task ticket?

**Expected Output:**
```json
{
 "relevant_elements": [
   {
     "element": {
       "id": "service_ticket",
       "name": "ticket",
       "description": "A send task that creates a queue item to a queue",
       "purpose": "Create a queue item within the workflow"
     },
     "extension_type": "queue"
   }
 ],
 "title": "Associate a queue to Ticket Task for creating an item",
 "explanation": ""
}
```

### **Example 9:**
**User Query:** can you suggest a queue for creating and waiting for a queue item for task ticket?

**Expected Output:**
```json
{
 "relevant_elements": [
   {
     "element": {
       "id": "service_ticket",
       "name": "ticket",
       "description": "A serivce task that creates a queue item and waits for its completion",
       "purpose": "Create a queue item within the workflow"
     },
     "extension_type": "queue",
     "wait_for_completion": true
   }
 ],
 "title": "Associate a queue to Ticket Task for creating an item and wait for its completion",
 "explanation": ""
}
```
import pathlib
import re
from typing import Tuple

import langchain.schema
import langchain.schema.messages

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    BpmnGenerationToolResult,
    BpmnRequestContext,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import (
    BpmnValidator,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

VALIDATOR = BpmnValidator()
LOGGER = AppInsightsLogger()


class BpmnGenerationFromImageTask(BpmnBaseTask):
    """Task for generating BPMN diagrams from images.

    This class handles the conversion of images to BPMN XML diagrams using
    large language models with vision capabilities.
    """

    def __init__(self) -> None:
        """Initialize the BPMN image generation task with required configuration."""
        super().__init__("generation_prompt.yaml")
        self.bpmn_image_example = BpmnBaseTask.read_content((pathlib.Path(__file__).parent / "config/bpmn_image_example.json").absolute())
        self.supported_element_examples = BpmnBaseTask.read_content((pathlib.Path(__file__).parent / "config/supported_element_examples.md").absolute())
        self.convert_image_bpmn_examples = BpmnBaseTask.read_content((pathlib.Path(__file__).parent / "config/convert_image_bpmn_examples.md").absolute())

    def _get_image_mime_type(self, base64_image: str) -> tuple[str, str | None]:
        """Determine the MIME type of an image from its base64 encoding.

        Base64-encoded images often have a prefix that indicates the image type.
        This method attempts to extract that information and validates against
        supported formats.

        Args:
            base64_image: Base64-encoded image string

        Returns:
            A tuple containing the MIME type string and an error message if any
            (e.g., ("image/png", None) or ("", "Unsupported image format"))
        """
        try:
            # List of supported MIME types
            SUPPORTED_MIME_TYPES = ["image/png", "image/jpeg", "image/gif", "image/webp"]

            if not base64_image:
                return "", "No image data provided"

            # Check if it's a data URL (e.g., "data:image/jpeg;base64,...")
            data_url_pattern = r"^data:image/([a-zA-Z0-9]+);base64,"
            match = re.match(data_url_pattern, base64_image)
            if match:
                mime_type = f"image/{match.group(1).lower()}"
                if mime_type in SUPPORTED_MIME_TYPES:
                    return mime_type, None
                else:
                    error_msg = f"Unsupported image format: {mime_type}"
                    LOGGER.warning(error_msg)
                    return "", error_msg

            return "", "Invalid image format: missing MIME type prefix"

        except Exception as e:
            # Catch any unexpected errors in the entire method
            error_msg = f"Unexpected error in image type detection: {str(e)}"
            LOGGER.error(error_msg)
            return "", error_msg

    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        """Generate a BPMN diagram from an image.

        Args:
            query: The user's query (not used - standard prompt is used instead)
            image_data: Base64-encoded image data

        Returns:
            A result object containing the generated BPMN diagram and metadata
        """
        # Always use the standard conversion prompt for consistency
        # This ensures reliable results regardless of what the user entered
        standard_query = "Convert this image to a proper BPMN diagram following BPMN 2.0 standard. Make sure to fully include bpmnDiagram as well."
        LOGGER.info(f"Using standard image conversion prompt instead of: {context.user_request}")

        # Handle potential image format errors
        if context.image_data:
            image_base64 = context.image_data.decode("utf-8")
            mime_type, error = self._get_image_mime_type(image_base64)
            if error:
                LOGGER.error(f"Bad request: {error}")
                return BpmnGenerationToolResult(
                    tool=Tool.CONVERT_IMAGE,
                    title="Unsupported Image Format",
                    explanation=f"The provided image format is not supported: {error}",
                    add=[],
                    update=[],
                    delete=[],
                )
        # If context provides google, use it; otherwise default to Anthropic as openai does not support image
        model_type = context.override_model_type if context.override_model_type == ModelType.Google else ModelType.Anthropic
        model_name = self._get_model_name(model_type)
        result, usage = await self._generate(standard_query, image_data=context.image_data, model_name=model_name)
        result_json = result.strip("```json\n").strip("\n```")

        # provide a default bpmn xml if current_bpmn is empty, then LLM can always generate patch
        current_bpmn = self.common_config["default_bpmn_xml"] if context.current_bpmn == "" else context.current_bpmn

        output: BpmnGenerationChatTaskResult = await self._validate_output(context.support_validation, current_bpmn, result_json, usage, model_name)

        deleted_elements = output.get("delete") or []
        process_id_to_delete = self.extract_process_id(current_bpmn)
        if process_id_to_delete:
            deleted_elements.append({"id": process_id_to_delete, "type": "bpmn:process"})

        return BpmnGenerationToolResult(
            tool=Tool.CONVERT_IMAGE,
            title=output.get("title"),
            explanation=output["explanation"],
            add=output.get("add") or [],
            update=output.get("update") or [],
            delete=deleted_elements,
        )

    @log_execution_time("bpmn_generation_from_image_task.generate")
    async def _generate(
        self,
        query: str,
        model_name: str,
        image_data: bytes | None = None,
    ) -> Tuple[str, TokenUsage]:
        """Generate BPMN XML from an image using an LLM with vision capabilities.

        Args:
            query: The prompt to send to the model
            gen_model: The LLM gateway model to use
            image_data: Base64-encoded image data

        Returns:
            A tuple containing the generated result and token usage statistics
        """
        # Use image_data directly as it's already in base64 format
        image_base64 = image_data.decode("utf-8") if image_data else None

        system_message = self._system_template(self.config["prompt"]["system_template"]["convert_image"]).format(
            supported_element_examples=self.supported_element_examples, convert_image_bpmn_examples=self.convert_image_bpmn_examples
        )

        # Determine the image type dynamically - error handling moved to generate method
        mime_type, _ = self._get_image_mime_type(image_base64) if image_base64 else ("image/png", None)
        LOGGER.info(f"Using image mime type: {mime_type}")

        user_message = langchain.schema.messages.HumanMessage(
            content=[
                {
                    "type": "text",
                    "text": query,
                },
                {
                    "type": mime_type,
                    "image_url": {"url": f"{image_base64}"},
                },
            ]
        )
        return await self._call_llm(system_message, user_message, model_name)

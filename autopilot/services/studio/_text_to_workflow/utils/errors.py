# 422 error code means request syntactically correct but we cannot process it due to some limitation on the server.
# In this case, either the plan generated was empty, or the some llm prompt was too long.
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/422

from typing import Dict

from openai import APIError, PermissionDeniedError


def build_error_body(msg: str) -> Dict[str, Dict[str, str]]:
    return {"error": {"message": msg}}


class ServiceError(Exception):
    def __init__(self, code: int, message: str):
        super().__init__(message)
        self.message = message
        self.code = code

    def to_error_body(self):
        return build_error_body(self.message)


class BadRequestError(ServiceError):
    def __init__(self, message: str):
        super().__init__(400, message)


class UnprocessableEntityError(ServiceError):
    def __init__(self, message: str):
        super().__init__(422, message)


class EmptyMethodsIndexError(Exception):
    def __init__(self, target_framework: str):
        super().__init__()
        self.message = f"No documents found for {target_framework}"


class EmptyActivitiesIndexError(Exception):
    def __init__(self, target_framework: str, activity_type: str):
        super().__init__()
        self.message = f"No activities found for {target_framework}.{activity_type}."


class ExistingWorkflowYAMLDeserializationError(BadRequestError):
    def __init__(self):
        super().__init__("The existing workflow could not be deserialized using pyyaml.")


class ExistingWorkflowEmptyError(BadRequestError):
    def __init__(self):
        super().__init__("The existing workflow YAML is empty.")


class ExistingWorkflowInvalidError(BadRequestError):
    def __init__(self, reason: str):
        super().__init__(f"The existing workflow YAML is invalid. Reason: {reason}")


class MissingCurrentActivityError(BadRequestError):
    def __init__(self):
        super().__init__("The existing workflow YAML does not contain a current activity.")


class MultipleCurrentActivitiesError(BadRequestError):
    def __init__(self):
        super().__init__("The existing workflow YAML contains multiple current activities.")


class MissingActivityTypedefError(BadRequestError):
    def __init__(self, activity_id: str):
        super().__init__(f"The workflow type definitions could not be loaded for {activity_id}.")


class EmptyPlanError(UnprocessableEntityError):
    def __init__(self):
        super().__init__("Could not create a valid plan.")


class PromptOverflowError(UnprocessableEntityError):
    def __init__(self):
        super().__init__("User request is too long. Please try again with a shorter request.")


class ContentFilterError(UnprocessableEntityError):
    def __init__(self):
        super().__init__("User request contains invalid words.")


class UnhandledPropertyError(UnprocessableEntityError):
    def __init__(self, activity_id: str, property_name: str):
        super().__init__(f"Unhandled property: {property_name} for activity: {activity_id}")


class FailedDependencyError(ServiceError):
    def __init__(self, message: str = ""):
        super().__init__(424, message or "Failed Dependency")


# used for specific features where rate limiting is done by LLM Gateway - like QA DOM
class LLMGWRateLimit(ServiceError):
    def __init__(self, message: str = ""):
        super().__init__(429, message or "Rate limit exceeded")


class DraftGenerationException(Exception):
    def __init__(self, message: str):
        super().__init__(message)


def extract_exception_summary(ex: Exception) -> tuple[dict, int]:
    content = {}
    status = 500

    if isinstance(ex, APIError):
        # This should ideally only happen on Governance policy restrictions
        if isinstance(ex, PermissionDeniedError):
            message = "Forbidden"
            if isinstance(ex.body, dict):
                message = ex.body.get("Message", message)
                # default error message from governance is "User is forbidden by governance policy to use: wingman"
                # replace this with proper product name
                message = message.replace("wingman", "Autopilot for Developers")
            content, status = build_error_body(message), getattr(ex, "status_code", 403)
        elif hasattr(ex, "response"):
            # Morph to Content Filter Error if it is a content filter error
            try:
                if ex.response.json().get("error", {}).get("code", "") == "content_filter" or "content filter" in str(ex).lower():
                    content, status = build_error_body(str(ContentFilterError().message)), ContentFilterError().code
                if ex.response.json().get("error", {}).get("code", "") == "context_length_exceeded":
                    content, status = build_error_body(str(PromptOverflowError().message)), PromptOverflowError().code
            except Exception:
                content, status = build_error_body(str(ex.message)), getattr(ex, "status_code", 500)
        else:
            content, status = build_error_body(str(ex.message)), getattr(ex, "status_code", 500)
    elif isinstance(ex, ValueError) and "content filter" in str(ex).lower():
        content, status = build_error_body(str(ContentFilterError().message)), ContentFilterError().code
    elif isinstance(ex, ServiceError):
        content, status = ex.to_error_body(), ex.code
    elif hasattr(ex, "status_code"):
        content, status = build_error_body(str(ex)), getattr(ex, "status_code", 500)
    elif hasattr(ex, "code"):
        content, status = build_error_body(str(ex)), getattr(ex, "code", 500)
    else:
        content, status = build_error_body("Internal Server Error"), 500

    return content, status

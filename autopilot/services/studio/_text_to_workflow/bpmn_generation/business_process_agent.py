import uuid

from langchain_core.runnables.config import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.graph import CompiledGraph

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_flow_task import BpmnGenerationFlowTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnRequestContext,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_qa_task import BpmnQATask
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_ack import BusinessProcessAgentAck
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState, BusinessProcessAssistantRequest
from services.studio._text_to_workflow.bpmn_generation.business_process_planner import BusinessProcessAgentPlanner
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_flow_task import BpmnExtensionFlowTask
from services.studio._text_to_workflow.utils.request_utils import get_request_context
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

LOGGER = AppInsightsLogger()


class BusinessProcessAgent:
    def __init__(self) -> None:
        self.ack = BusinessProcessAgentAck()
        self.planner = BusinessProcessAgentPlanner()
        self.qa = BpmnQATask()
        self.bpmn_editor = BpmnGenerationFlowTask()
        self.extension_editor = BpmnExtensionFlowTask()
        self.graph = self._build_agent_graph()
        self.sessions = {}

    async def dummy(self, state: BusinessProcessAgentState, config: RunnableConfig):
        pass

    def find_next_for_planner(self, state: BusinessProcessAgentState):
        nodes_to_run = state["nodes_to_run"] or []

        nodes_with_priority = ["bpmn-editor", "extension-editor", "qa"]
        for node in nodes_with_priority:
            if node in nodes_to_run:
                return node

        return "dummy"

    def find_next_for_bpmn_editor(self, state: BusinessProcessAgentState):
        if state.get("tool_result") is None:
            return "dummy"

        nodes_to_run = state["nodes_to_run"] or []

        if "extension-editor" in nodes_to_run:
            return "extension-editor"
        elif "qa" in nodes_to_run:
            return "qa"
        else:
            return "dummy"

    def find_next_for_extension_editor(self, state: BusinessProcessAgentState):
        if state.get("tool_result") is None:
            return "dummy"

        nodes_to_run = state["nodes_to_run"] or []

        if "qa" in nodes_to_run:
            return "qa"
        else:
            return "dummy"

    def _build_agent_graph(self) -> CompiledGraph:
        memory = MemorySaver()
        builder = StateGraph(BusinessProcessAgentState)

        builder.add_node("ack", self.ack.run)
        builder.add_node("planner", self.planner.run)
        builder.add_node("bpmn-editor", self.bpmn_editor.run)
        builder.add_node("extension-editor", self.extension_editor.run)
        builder.add_node("qa", self.qa.run)
        builder.add_node("dummy", self.dummy)

        builder.add_edge(START, "ack")
        builder.add_edge(START, "planner")

        builder.add_conditional_edges("planner", self.find_next_for_planner)
        builder.add_conditional_edges("bpmn-editor", self.find_next_for_bpmn_editor)
        builder.add_conditional_edges("extension-editor", self.find_next_for_extension_editor)

        builder.add_edge("qa", "dummy")
        builder.add_edge(["ack", "dummy"], END)

        return builder.compile(checkpointer=memory)

    @log_execution_time("bpmn_agent.start")
    async def start(self, request: BusinessProcessAssistantRequest, message_emitter: MessageEmitter):
        session_id = request.sessionId or str(uuid.uuid4())
        LOGGER.info(f"Processing BPMN assistant request with sessionId={session_id},solutionId={request.solutionId} and projectKey={request.projectKey}")
        config = RunnableConfig(configurable={"thread_id": session_id, "message_emitter": message_emitter})
        agent_state = BusinessProcessAgentState(
            request=request,
            planner_result=None,
            context=BpmnRequestContext(
                user_request="",
                current_bpmn="",
                request_context=get_request_context(),
                image_data=None,
                chat_history=None,
                extension_data_override=None,
                support_validation=False,
            ),
            nodes_to_run=None,
            current_tool_index=-1,
            tool_result=None,
            usage_token=[],
        )

        await self.graph.ainvoke(agent_state, config, stream_mode="updates")

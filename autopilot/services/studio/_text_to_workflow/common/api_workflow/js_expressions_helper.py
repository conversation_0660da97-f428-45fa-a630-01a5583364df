from typing import Optional

import esprima

from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class JavaScriptExpressionsHelper:
    @staticmethod
    def validate_js_expression(expression: str, variables: set[str]) -> tuple[Optional[str], str]:
        """
        Validates a JavaScript expression using pyminiracer.
        Returns a tuple of (is_valid, error_message).
        If valid, error_message will be empty.
        """
        # we must wrap the expression in a function
        method_signature = f"function x({', '.join([f'{variable}' for variable in variables])})"

        js_code = "{method_signature} {{ return eval(({expression}));}}".format(method_signature=method_signature, expression=expression)
        # esprima does not support "?." or "??" operator so we replace it with "."
        js_code = js_code.replace("?.", ".")
        js_code = js_code.replace("??", "||")

        return JavaScriptExpressionsHelper._validate_js_method(js_code), js_code

    @staticmethod
    def validate_js_invoke(expression: str, variables: set[str]) -> tuple[Optional[str], str]:
        """
        Validates a JavaScript invoke result
        """
        method_signature = f"function x({', '.join([f'{variable}' for variable in variables])})"
        js_code = f"{method_signature} {{{expression}}}"
        return JavaScriptExpressionsHelper._validate_js_method(js_code), js_code

    @staticmethod
    def _validate_js_method(js_code: str) -> Optional[str]:
        """
        Validate a JavaScript method using pyminiracer.
        """
        try:
            esprima.parseScript(js_code)
            return None
        except Exception as e:
            LOGGER.error(f"JavaScript code validation failed: {e}")
            return str(e)

import pytest

from services.studio._text_to_workflow.common.activity_retriever import <PERSON>Retriever
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils import yaml_utils as yu
from services.studio._text_to_workflow.utils.paths import get_wf_gen_activity_retriever_dataset_path
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.services.helpers.ignored_activities_helper import get_ignored_activities_map
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_draft_service import WorkflowGenerationDraftService
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent

_draft_fixtures_path = conftest._fixtures_path / "draft_fixtures"
_draft_query_samples_path = _draft_fixtures_path / "query_samples.yaml"

_query_samples = (
    "basic_deserialization",
    "gsuite_trigger",
)


@pytest.fixture(scope="module")
def draft_query_samples() -> dict:
    return yu.yaml_load(_draft_query_samples_path)


def load_demonstration(demonstration_path: str) -> dict:
    path = get_wf_gen_activity_retriever_dataset_path() / demonstration_path
    return yu.yaml_load(path)


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _query_samples)
async def test_draft_service(case: str, draft_query_samples: dict):
    test_data: dict = draft_query_samples[case]

    activities_retriever = ActivitiesRetriever()

    dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(activities_retriever)
    postprocess_component = WorkflowGenerationPostProcessComponent(
        activities_retriever, dynamic_activities_component, get_ignored_activities_map(activities_retriever)
    )

    prompt_builder_component = WorkflowGenerationPromptBuilderComponent(activities_retriever)

    draft_service = WorkflowGenerationDraftService(activities_retriever, postprocess_component, prompt_builder_component)
    await draft_service.init_and_load()

    # prepare dummy activity retrieval result
    _, connections = get_connections_data()
    activity_retrieval_result = ActivityRetrievalResult(
        generation=None,
        prompt=None,
        token_usage=None,
        proposed_triggers=None,
        proposed_activities=None,
        generation_details=None,
        unprocessed_retrieved_triggers=None,
        unprocessed_retrieved_activities=None,
        retrieved_activities=test_data["retrieved_activities"],
        retrieved_triggers=test_data["retrieved_triggers"],
        demonstrations=[load_demonstration(demo_path) for demo_path in test_data["demonstrations"]],
        query_embedding=None,
        connections_embedding=None,
        connections_by_key=None,
        ignored_activities=get_ignored_activities_map(activities_retriever)[test_data["target_framework"]],
    )

    result = await draft_service.generate_workflow_draft(
        query=test_data["query"],
        workflow=Workflow("", "", test_data["workflow"]) if "workflow" in test_data else None,
        connections=connections,
        variables=[],
        objects=[],
        mode=test_data["mode"],
        target_framework=test_data["target_framework"],
        activity_retrieval_result=activity_retrieval_result,
        localization="en",
        additional_type_definitions="",
        user_prompt_additional_instructions="",
    )

    # check the output wf contains the expected activities and triggers
    valid_wf = yu.yaml_load(result.result_workflow_details.workflow_generation["workflow_valid"])
    expected_solution: dict = test_data["solution"]
    trigger_name = valid_wf.get("trigger", {}).get("activity", None)

    if trigger_name is None:
        assert expected_solution["trigger"] is None
    else:
        assert trigger_name == expected_solution["trigger"]

    for i, predicted_node in enumerate(valid_wf["workflow"]):
        expected_node = expected_solution["activities"][i]
        assert predicted_node["activity"] == expected_node

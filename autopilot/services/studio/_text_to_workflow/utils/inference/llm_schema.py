from collections import defaultdict
from dataclasses import dataclass
from enum import Enum

import typing_extensions as t


class TokenU<PERSON><PERSON><PERSON><PERSON>(t.TypedDict):
    model: str
    promptTokens: int
    completionTokens: int
    totalTokens: int


@dataclass
class TokenUsage:
    model: str = ""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0

    def to_json(self) -> TokenUsageJson:
        return {
            "model": self.model,
            "promptTokens": self.prompt_tokens,
            "completionTokens": self.completion_tokens,
            "totalTokens": self.total_tokens,
        }


class ConsumingFeatureType(Enum):
    DEFAULT = "default"
    WORKFLOW_GENERATION = "workflow-generation"
    SEQUENCE_GENERATION = "sequence-generation"
    EXPRESSION_GENERATION = "expression-generation"
    FIX_EXPRESSION = "fix-expression"
    CODE_GENERATION = "code-generation"
    FIX_CODE = "fix-code"
    UI_AUTOMATION = "ui-automation"
    WORKFLOW_SUMMARIZATION = "workflow-summarization"
    API_WORKFLOW_SUMMARIZATION = "api-workflow-summarization"
    ACTIVITY_CONFIGURATION = "activity-configuration"
    AGENT_GENERATION = "agent-generation"
    SEMANTIC_DIFF = "semantic-diff"
    TESTDATA_GENERATION = "testdata-generation"
    WORKFLOW_FIX = "workflow-fix"
    QA_ELEMENT = "qa-element"
    QA_SCREEN = "qa-screen"
    GET_SEMANTIC_DESCRIPTION = "get-semantic-description"
    CLOSE_POPUP = "close-popup"
    AGENT_EVALUATION = "agent-evaluation"
    BPMN_GENERATION = "bpmn-generation"
    BPMN_EVALUATION = "bpmn-evaluation"
    BPMN_EXTENSION_EVALUATION = "bpmn-extension-evaluation"
    SCREEN_AGENT = "screen-agent"
    SCREEN_AGENT_INFO_EXTRACTION = "screen-agent-info-extraction"
    API_ACTIVITY_EDIT = "api-activity-edit"
    PREDICT_EXPRESSION = "predict-expression"


CONSUMING_TYPE_FRIENDLY_ACTION_NAME = defaultdict(
    lambda: "Autopilot Action",
    {
        ConsumingFeatureType.DEFAULT: "Autopilot Action",
        ConsumingFeatureType.WORKFLOW_GENERATION: "Workflow Generation",
        ConsumingFeatureType.SEQUENCE_GENERATION: "Sequence Generation",
        ConsumingFeatureType.EXPRESSION_GENERATION: "Expression Generation",
        ConsumingFeatureType.FIX_EXPRESSION: "Expression Fix",
        ConsumingFeatureType.CODE_GENERATION: "Code Generation",
        ConsumingFeatureType.UI_AUTOMATION: "UI Actions Generation",
        ConsumingFeatureType.WORKFLOW_SUMMARIZATION: "Workflow Summarization",
        ConsumingFeatureType.ACTIVITY_CONFIGURATION: "Activity Configuration",
        ConsumingFeatureType.AGENT_GENERATION: "Agent Generation",
        ConsumingFeatureType.SEMANTIC_DIFF: "Semantic Diff",
        ConsumingFeatureType.TESTDATA_GENERATION: "Testdata Generation",
        ConsumingFeatureType.WORKFLOW_FIX: "Workflow Fix",
        ConsumingFeatureType.QA_ELEMENT: "Element QA",
        ConsumingFeatureType.QA_SCREEN: "Screen QA",
        ConsumingFeatureType.GET_SEMANTIC_DESCRIPTION: "Generate Semantic Description",
        ConsumingFeatureType.CLOSE_POPUP: "Close pop-up",
        ConsumingFeatureType.AGENT_EVALUATION: "Agent Evaluation",
        ConsumingFeatureType.PREDICT_EXPRESSION: "Predict Expression",
    },
)

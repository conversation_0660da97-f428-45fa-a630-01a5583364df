import json

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.state import State
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import parse_message_json

system_template = """You are a computer use agent that perform computer-related tasks.
You will be given a task, a list of previous actions and previously extracted data, the current screen image.
Your task is to compute the final task output based on the current information.
Provide a detailed and accurate response.
"""

user_message_template = """Here are the current information:
Task: {task}

Previous actions:
{history}

Compute the final task output with the following json object with format:
{{
    "output": str or json object ## the final output of the task
}}
You can choose to weather return the final output as simple text or a json object when the task is to extract some structured data.
"""


class OutputComplier(object):
    def __init__(self, options: dict):
        self.options = options

    def build_messages(self, state: State, history: str) -> list[dict]:
        system_message = {
            "role": "system",
            "content": system_template.format(),
        }

        contents = [
            {
                "type": "text",
                "text": user_message_template.format(
                    task=state.task,
                    history={history},
                ),
            },
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{state.image_base64}"},
            },
        ]
        user_message = {
            "role": "user",
            "content": contents,
        }
        messages = [system_message, user_message]
        return messages

    async def predict(self, state: State, history: str, model_name: str, metadata: dict) -> str:
        messages = self.build_messages(state, history)
        response_content = await llm_client.send_messages(
            messages,
            options={"model_name": model_name},
            max_tokens=3000,
            metadata={**metadata, "generation_name": "output_compiler"},
        )
        if response_content is None:
            raise ValueError("Output complier is None")

        console_logger.debug("Output complier response: %s", response_content)
        try:
            result = parse_message_json(str(response_content))
            output = json.dumps(result["output"], indent=2, ensure_ascii=False)
        except Exception:
            output = response_content
        return output

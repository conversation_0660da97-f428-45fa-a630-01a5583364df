from enum import Enum
from typing import Optional

import typing_extensions as t
from pydantic import BaseModel, Field

from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.request_schema import BaseRequest, BaseResponse


class ExpressionLanguageType(Enum):
    CSharp = "csharp"
    VBNET = "vbnet"
    JavaScript = "javascript"
    JQ = "jq"


class SourceType(Enum):
    Workflow = "workflow"
    BPMN = "bpmn"
    ApiWorkflow = "api_workflow"
    JsInvoke = "js_invoke"


class BaseExpressionRequest(BaseRequest):
    additionalTypeDefinitions: str | None
    availableVariables: list | None
    expressionLanguage: str
    expressionTypeDefinition: str | None
    currentExpression: str | None
    source: t.NotRequired[SourceType]
    outputType: t.NotRequired[str]


class ExpressionGenerationRequest(BaseExpressionRequest):
    userRequest: str


class ExpressionGenerationFixRequest(BaseExpressionRequest):
    currentError: str


class ExpressionGenerationModelResponse(BaseModel):
    explanation: str = Field(description="The explanation of the expression")
    expression: str = Field(description="The expression to be generated")
    sampleInput: Optional[str] = Field(None, description="Sample input data for the variables used in the expression")


class ExpressionGenerationTaskResult(t.TypedDict):
    result: ExpressionGenerationModelResponse
    usage: TokenUsage


class ExpressionGenerationResponse(BaseResponse):
    result: str

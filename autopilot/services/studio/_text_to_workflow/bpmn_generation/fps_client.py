import json
from typing import Any

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import ExtensionTypes, ResourceKinds, ResourceTypes
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    ActionApp,
    Activity,
    Connection,
    Connector,
    ConnectorElement,
    ConnectorObject,
    ExtensionType,
    Folder,
    Process,
    SolutionResource,
    SolutionResourceRequest,
    Trigger,
)
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.http_utils import http_get_json, http_post_json
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time

MAX_TOP = 500


@log_execution_time("fps_client.get_processes_and_agents")
async def get_processes_and_agents(
    context: RequestContext,
    process_type: str | None = None,
) -> list[Process]:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/{}/orchestrator_/odata/Releases/UiPath.Server.Configuration.OData.ListReleases".format(settings.CLOUD_URL_BASE, organization_id, tenant_id)
    params = {
        "$select": "Id,Key,ProcessKey,Name,Description,Tags,ProcessType",
        "$top": MAX_TOP,
    }
    if process_type:
        params["$filter"] = "ProcessType eq '{}'".format(process_type)
    else:
        params["$filter"] = "((ProcessType eq 'Process') or (ProcessType eq 'Agent'))"

    headers = {
        "Authorization": raw_jwt,
    }
    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params)
    processes: list[Process] = []

    if not isinstance(data, dict):
        raise ValueError(
            f"Expected dictionary response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}"
        )

    for process in data.get("value", []):
        desc = process.get("Description")
        if desc == "N/A":
            desc = None
        processes.append(
            Process(
                id=process.get("Key"),
                name=process.get("Name"),
                type=ExtensionTypes.AGENT if process.get("ProcessType") == "Agent" else ExtensionTypes.PROCESS,
                description=desc,
                fakeId=None,
                score=0,
                connection=None,
                activity=None,
            )
        )
    return processes


@log_execution_time("fps_client.get_action_apps")
async def get_action_apps(
    context: RequestContext,
) -> list[ActionApp]:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/apps_/default/api/v1/default/action-apps".format(settings.CLOUD_URL_BASE, organization_id)
    params = {
        "state": "deployed",
    }
    headers = {
        "Authorization": raw_jwt,
        "x-uipath-tenantid": tenant_id,
    }
    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params)
    apps: list[ActionApp] = []

    if not isinstance(data, dict):
        raise ValueError(
            f"Expected dictionary response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}"
        )

    for action_app in data.get("deployed", []):
        apps.append(
            ActionApp(
                id=action_app.get("id"),
                name=action_app.get("deploymentTitle"),
                systemName=action_app.get("systemName"),
                deployVersion=action_app.get("deployVersion"),
                fakeId=None,
                score=0,
                type=ExtensionTypes.ACTION,
            )
        )
    return apps


@log_execution_time("fps_client.get_connectors_with_connections")
async def get_connection(
    context: RequestContext,
    connector: str,
) -> Connection | None:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/{}/connections_/api/v1/Connectors/{}/connections".format(settings.CLOUD_URL_BASE, organization_id, tenant_id, connector)
    params = {
        "allFolders": "true",
        "folderDefaults": "true",
    }
    headers = {
        "Authorization": raw_jwt,
    }

    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params)

    if not isinstance(data, list):
        raise ValueError(f"Expected list response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}")

    res = None
    for raw_connection in data:
        is_default = raw_connection.get("isDefault", False)
        connection = Connection(
            id=str(raw_connection.get("id", "")),
            name=str(raw_connection.get("name", "")),
            isDefault=is_default,
            state=str(raw_connection.get("state", "")),
            instanceId=raw_connection.get("elementInstanceId", 0),
        )
        if not res:
            res = connection
        if is_default:
            return connection
    return res


@log_execution_time("fps_client.get_activities_and_triggers")
async def get_activities_and_triggers(
    context: RequestContext,
    connector: str,
) -> tuple[list[Activity], list[Trigger]]:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/{}/elements_/v3/element/elements/{}/activities".format(settings.CLOUD_URL_BASE, organization_id, tenant_id, connector)
    headers = {
        "Authorization": raw_jwt,
    }
    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params=None)

    if not isinstance(data, list):
        raise ValueError(f"Expected list response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}")

    activities: list[Activity] = []
    triggers: list[Trigger] = []
    for raw_activity in data:
        if raw_activity["isTrigger"]:
            trigger = Trigger(
                name=str(raw_activity.get("name", "")),
                displayName=str(raw_activity.get("displayName", "")),
                description=str(raw_activity.get("description", "")),
                objectName=str(raw_activity.get("objectName", "")),
            )
            triggers.append(trigger)
        else:
            activity = Activity(
                name=str(raw_activity.get("name", "")),
                displayName=str(raw_activity.get("displayName", "")),
                description=str(raw_activity.get("description", "")),
            )
            activities.append(activity)
    return activities, triggers


@log_execution_time("fps_client.get_objects")
async def get_objects(
    context: RequestContext,
    connector: str,
) -> list[ConnectorObject]:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/{}/elements_/v3/element/elements/{}/objects".format(settings.CLOUD_URL_BASE, organization_id, tenant_id, connector)
    headers = {
        "Authorization": raw_jwt,
    }
    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params=None)

    if not isinstance(data, list):
        raise ValueError(f"Expected list response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}")

    objects: list[ConnectorObject] = []

    for raw_object in data:
        if not raw_object["isHidden"] and str(raw_object.get("subType", "")) == "AgentExecution":
            trigger = ConnectorObject(
                name=str(raw_object.get("name", "")),
                displayName=str(raw_object.get("displayName", "")),
                executionType=str(raw_object.get("executionType", "")),
            )
            objects.append(trigger)
    return objects


@log_execution_time("fps_client.get_connector_elements")
async def get_connector_elements(
    context: RequestContext,
) -> tuple[list[ConnectorElement], list[ConnectorElement]]:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/{}/elements_/v3/element/elements".format(settings.CLOUD_URL_BASE, organization_id, tenant_id)
    params = {
        "pageSize": "1000",
    }
    headers = {
        "Authorization": raw_jwt,
    }
    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params)

    if not isinstance(data, list):
        raise ValueError(f"Expected list response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}")

    connectors: list[ConnectorElement] = []
    external_agents: list[ConnectorElement] = []
    for raw_connector in data:
        connector: ConnectorElement = ConnectorElement(
            key=str(raw_connector.get("key", "")),
            name=str(raw_connector.get("name", "")),
            description=str(raw_connector.get("description", "")),
        )

        product_lines = raw_connector.get("productLines", {}).get("include", [])
        if ("Agents" in product_lines) or "Agent" in product_lines:
            external_agents.append(connector)
        else:
            connectors.append(connector)

    return connectors, external_agents


@log_execution_time("fps_client.get_solution_resources")
async def get_solution_resources(
    context: RequestContext,
    solutionId: str,
    projectKey: str,
    kind: ResourceKinds,
    types: list[ResourceTypes] | None = None,
) -> list[SolutionResource]:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/studio_/backend/api/resourcebuilder/solutions/{}/resources/search".format(settings.CLOUD_URL_BASE, organization_id, solutionId)
    params: dict = {
        "kind": kind.value,
        "includeSolutionResources": "true",
        "projectKey": projectKey,
        "pageSize": 50,
    }
    if types and len(types) > 0:
        params["types"] = [type.value for type in types]
    headers = {
        "Authorization": raw_jwt,
        "x-uipath-tenantid": tenant_id,
    }
    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params)
    if not isinstance(data, dict):
        raise ValueError(
            f"Expected dictionary response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}"
        )

    resources: list[SolutionResource] = []
    _to_solution_resource(data.get("solutionResources", []), kind=kind, result=resources)
    available_resources = data.get("availableResources", [])
    if len(available_resources) > 0:
        _to_solution_resource(available_resources[0].get("resources", []), kind=kind, result=resources)

    return resources


def _to_solution_resource(resources: list, kind: ResourceKinds, result: list[SolutionResource]):
    for resource in resources:
        resource_type = resource.get("type", kind)
        raw_folder = resource.get("folders")[0]
        path = raw_folder.get("path")
        folder_key = path.split(".")[-1]
        result.append(
            SolutionResource(
                id=resource.get("key"),
                name=resource.get("name"),
                description=resource.get("description"),
                fakeId=None,
                score=0,
                type=resource_type if resource_type else kind.value,
                resourceType=resource_type,
                kind=resource.get("kind", kind),
                folder=Folder(folderKey=folder_key, path=path, fullyQualifiedName=raw_folder.get("fullyQualifiedName")),
                inputJson=None,
            )
        )


@log_execution_time("fps_client.get_business_rules")
async def get_business_rules(context: RequestContext) -> list[SolutionResource]:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""
    url = "{}/{}/{}/resourcecatalog_/Entities/BusinessRule".format(settings.CLOUD_URL_BASE, organization_id, tenant_id)
    params = {
        "take": "100",
    }
    headers = {"Authorization": raw_jwt}
    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params)
    business_rules: list[SolutionResource] = []

    if not isinstance(data, dict):
        raise ValueError(
            f"Expected dictionary response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}"
        )

    for business_rule in data.get("value", []):
        business_rules.append(
            SolutionResource(
                id=business_rule.get("entityKey"),
                name=business_rule.get("name"),
                description=business_rule.get("description"),
                fakeId=None,
                score=0,
                type=ExtensionTypes.BUSINESS_RULE,
                kind=None,
                folder=None,
                inputJson=None,
                resourceType=None,
            )
        )
    return business_rules


@log_execution_time("fps_client.get_extension_reference_key")
async def get_extension_reference_key(context: RequestContext, solution_id: str, resource: SolutionResource) -> str:
    if not resource.kind or not resource.folder:
        raise ValueError(f"Resource {resource.id} is missing kind or folder information. Cannot get reference key.")

    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""

    url = "{}/{}/studio_/backend/api/resourcebuilder/solutions/{}/resources/reference".format(settings.CLOUD_URL_BASE, organization_id, solution_id)

    headers = {"Authorization": raw_jwt, "x-uipath-tenantid": tenant_id, "Content-Type": "application/json"}
    request = SolutionResourceRequest(key=resource.id, type=resource.resourceType, kind=resource.kind.value, folder=resource.folder)
    data = await http_post_json(url, headers, json_obj=request)

    if not isinstance(data, dict):
        raise ValueError(
            f"Expected dictionary response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}"
        )

    return data.get("key", "")


@log_execution_time("fps_client.get_connector_metadata")
async def get_connector_metadata(context: RequestContext, connector: Connector) -> str:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""

    headers = {"Authorization": raw_jwt, "x-uipath-tenantid": tenant_id, "Content-Type": "application/json"}

    if connection := connector.connection:
        instance_id = connection.instanceId
    else:
        raise ValueError(f"Connector {connector.key} is missing connection information. Cannot get metadata.")

    if connector.activity:
        if activity := connector.activity:
            activity_key = activity.name
        else:
            raise ValueError(f"Connector {connector.key} is missing activity information. Cannot get metadata.")

        url = "{}/{}/{}/elements_/v3/element/instances/{}/elements/{}/activities/{}/metadata".format(
            settings.CLOUD_URL_BASE, organization_id, tenant_id, instance_id, connector.key, activity_key
        )

        res_json = await http_get_json(url, headers, params=None)
        return json.dumps(res_json)

    elif connector.trigger:
        url = "{}/{}/{}/connections_/api/v1/Connections/{}/operations".format(settings.CLOUD_URL_BASE, organization_id, tenant_id, connector.connection.id)

        res_json = await http_get_json(url, headers, params={"allFolders": True})
        if not isinstance(res_json, list):
            raise ValueError(
                f"Expected list response from FPS API, got {type(res_json).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}"
            )

        trigger_obj_name = connector.trigger.objectName
        matched_metadata = next((x for x in res_json if x.get("objectName") == trigger_obj_name), None)
        return json.dumps(matched_metadata)
    else:
        raise ValueError(f"Connector {connector.key} is missing activity or trigger information. Cannot get metadata.")


@log_execution_time("fps_client.get_extension_configuration")
async def get_extension_configuration_input_schema_raw(context: RequestContext, solution_id: str, resource_key: str, extension_type: ExtensionType) -> str:
    organization_id = context.organization_id
    tenant_id = context.tenant_id
    raw_jwt = context.raw_jwt or ""

    url = "{}/{}/studio_/backend/api/resourcebuilder/solutions/{}/resources/{}/configuration".format(
        settings.CLOUD_URL_BASE, organization_id, solution_id, resource_key
    )

    headers = {
        "Authorization": raw_jwt,
        "x-uipath-tenantid": tenant_id,
    }

    data: dict[str, Any] | list[dict[str, Any]] = await http_get_json(url, headers, params=None)

    if not isinstance(data, dict):
        raise ValueError(
            f"Expected dictionary response from FPS API, got {type(data).__name__}. URL: {url}, Organization: {organization_id}, Tenant: {tenant_id}"
        )

    if extension_type == ExtensionType.ACTION:
        return data.get("actionSchema", "")
    elif extension_type == ExtensionType.QUEUE:
        spec = data.get("spec", "")
        if spec:
            return spec.get("specificDataJsonSchema", "")
        return ""
    elif extension_type in [ExtensionType.RPA_WORKFLOW, ExtensionType.AGENT, ExtensionType.API_WORKFLOW, ExtensionType.AGENTIC_PROCESS]:
        spec = data.get("spec", "")
        if spec:
            input_schema_v2 = spec.get("inputArgumentsSchemaV2", "")
            input_schema = spec.get("inputArgumentsSchema", "")

            if input_schema_v2:
                return input_schema_v2
            if input_schema:
                return input_schema
        return ""
    else:
        raise NotImplementedError(f"Support for extension type: {extension_type} is not implemented yet.")

    # TODO: handle CONNECTOR, BUSINESS_RULE

import json
import os
import pathlib
from itertools import groupby

from langchain_core.runnables.config import RunnableConfig

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import CANNOT_PROCESS_REQUEST_EXPLANATION
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationChatTaskResult,
    ChatHistory,
    LightModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import (
    BpmnValidator,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState, ProcessType
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)

VALIDATOR = BpmnValidator()
LOGGER = AppInsightsLogger()


class BusinessProcessAgentAck(BpmnBaseTask):
    def __init__(self) -> None:
        super().__init__("ack_prompt.yaml")
        self.ack_examples = BpmnBaseTask.read_content(pathlib.Path(os.path.join(os.path.dirname(__file__), "config", "ack_examples.md")))
        self.history_dependencies = [Tool.QA, Tool.EDIT_BPMN, Tool.EXTENSION]

    @log_execution_time("business_process_agent_ack.run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig) -> dict:
        message_emitter = config.get("configurable", {})["message_emitter"]

        request = state["request"]
        if request is None:
            LOGGER.error("Request is None, cannot process the request.")
            await message_emitter.emit_message(CANNOT_PROCESS_REQUEST_EXPLANATION, end_stream=True)
            return {"tool_result": ToolResult(tool=Tool.ACK, explanation=CANNOT_PROCESS_REQUEST_EXPLANATION)}

        history_by_tool = {}
        all_histories = request.chatHistory or []
        all_histories.sort(key=lambda h: h["tool"])
        for tool_name, tool_histories in groupby(all_histories, key=lambda ch: ch["tool"]):
            tool_histories = list(tool_histories)
            history_by_tool[tool_name] = tool_histories
        result: BpmnGenerationChatTaskResult = await self._generate_ack(request.userRequest, request.processType, request.currentProcess or "", history_by_tool)

        await message_emitter.emit_message(result["explanation"])

        return {"tool_result": ToolResult(tool=Tool.ACK, explanation=result["explanation"])}

    async def _generate_ack(
        self, query: str, process_type: ProcessType, current_process: str, chat_history: dict[Tool, list[ChatHistory]] | None
    ) -> BpmnGenerationChatTaskResult:
        current_process = current_process.strip() if current_process is not None else ""
        # provide a default bpmn xml if current_bpmn is empty, then LLM can always generate patch
        if process_type == ProcessType.BPMN and current_process == "":
            current_process = self.common_config["default_bpmn_xml"]
        elif process_type == ProcessType.DMN and current_process == "":
            current_process = self.common_config["default_dmn"]

        system_message = self._system_template(self.config["prompt"]["system_template"]["ack"]).format(ack_examples=self.ack_examples)

        model_name = self._get_light_model_name(LightModelType.GoogleFlash)
        chat_history_str = await self._get_related_chat_history(chat_history, self.history_dependencies, model_name)
        user_message = self._human_template(self.config["prompt"]["user_template"]["ack"]).format(
            current_process=current_process, query=query, chat_history=chat_history_str
        )

        result, usage = await self._call_llm(system_message, user_message, model_name)
        result_json = result.strip("```json\n").strip("\n```")
        output: BpmnGenerationChatTaskResult = json.loads(result_json)
        output["usage"] = usage

        validation_result = VALIDATOR.validate_qa_json(result_json)
        if not validation_result["valid"]:
            LOGGER.error(f"JSON isn't valid: {validation_result['error_message']}. Failed to generate valid Ack. An exception occurred.")
            return BpmnGenerationChatTaskResult(add=None, update=None, delete=None, explanation=CANNOT_PROCESS_REQUEST_EXPLANATION, title=None, usage=usage)

        return output

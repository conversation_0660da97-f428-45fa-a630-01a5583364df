from dataclasses import dataclass
from typing import Any, List, Tuple

from langchain_core.prompt_values import PromptValue

from services.studio._text_to_workflow.common.schema import ActivityDefinition
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WorkflowProcessingError


@dataclass
class DraftGenerationEval:
    """This class is used to store the evaluation results for the draft generation task."""

    query: str
    ground_truth_activities: List[str]
    solution_workflow: Any
    existing_workflow_sequence: Any | None
    elapsed_time: float
    processed_draft_workflow: Any

    # Properties extracted from generation_result and result_workflow_details
    raw_workflow: Any
    post_generation_errors: list[WorkflowProcessingError] | None
    proposed_activities: list[ActivityDefinition]  # Combined activity_defs and trigger_defs
    token_usage: TokenUsage
    demonstrations: list
    prompt: PromptValue
    raw_model_prediction: str  # Raw model output before processing

    ted_nodes_mapping: list[Tuple[str, str, float, float]]

    # Levenshtein scores obtained from levenstein string comparison of the generated workflow with the ground truth workflow
    lev_score: float
    lev_score_no_extras: float

    activity_tree_accuracy: float

    # Tree edit distance computation of the generated workflow with the ground truth workflow
    ted_score: float

    # Tree edit distance computation of the params using levenstein distance
    ted_levenstein_params_score: float
    levenstein_params_score: float

    # Tree edit distance computation of the params using exact match
    ted_exact_params_score: float
    exact_params_score: float

from typing_extensions import override

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import BpmnTaskTypes, ExtensionTypes, ImplementationTypes
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionPlan,
    BpmnRequestContext,
    ExtensionDataOverride,
    ExtensionInfo,
    SolutionResource,
)
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_solution_resource_task import BpmnExtensionSolutionResourceTask
from services.studio._text_to_workflow.bpmn_generation.fps_client import get_business_rules
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()


class BpmnExtensionBusinessRuleTask(BpmnExtensionSolutionResourceTask):
    def __init__(self):
        super().__init__(ImplementationTypes.BUSINESS_RULE, None, solution_types=None)

    @override
    def _get_task_type(self, plan: BpmnExtensionPlan) -> str:
        return BpmnTaskTypes.BUSINESS_RULE

    @override
    def _get_extension_type(self, plan: BpmnExtensionPlan) -> str:
        return ExtensionTypes.BUSINESS_RULE

    @override
    def _get_eval_dataset(self, extension_data_override: ExtensionDataOverride) -> list[SolutionResource]:
        return []

    @log_execution_time("bpmn_extension_business_rule_task.gather_resources")
    @override
    async def _gather_resources(self, context: BpmnRequestContext) -> tuple[list[ExtensionInfo], dict[int, SolutionResource]]:
        request_context = context.request_context
        extension_data = context.extension_data_override
        if extension_data:
            business_rules = self._get_eval_dataset(extension_data)
        else:
            business_rules = await get_business_rules(request_context)

        # Build simpler data structure for processes and agents and fake integer id for reducing token size
        fake_id = 1
        business_rules_by_fake_id: dict[int, SolutionResource] = {}
        candidates: list[ExtensionInfo] = []
        for business_rule in business_rules:
            business_rule.fakeId = fake_id
            candidates.append(ExtensionInfo(id=fake_id, name=business_rule.name, description=business_rule.description, score=None))
            business_rules_by_fake_id[business_rule.fakeId] = business_rule
            fake_id += 1

        return candidates, business_rules_by_fake_id

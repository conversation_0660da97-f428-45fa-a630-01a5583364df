from typing import Optional

from pydantic import Field, ValidationInfo, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # FastAPI
    PROJECT_NAME: str = "Autopilot Studio Service"
    DEBUG_MODE: bool = False  # On true will lazy load the state stores and show progress bars

    # App
    APP_INSIGHTS_INSTRUMENTATION_KEY: Optional[str] = Field(default=None, alias="APP_INSIGHTS_KEY")
    APP_INSIGHTS_CONNECTION_STRING: Optional[str] = None
    EMBEDDINGS_STORAGE_ACCOUNT_CONNECTION_STRING: str = ""
    DEFAULT_SEED: int = Field(default=42, alias="DEFAULT_MODEL_SEED")
    CLOUD_URL_BASE: str = "https://staging.uipath.com"
    RUN_EMBEDDINGS_BACKGROUND_REBUILD: bool = False
    IS_PROD: Optional[bool] = None
    IS_DEVELOPMENT: bool = False
    USE_CACHED_CONNECTIONS: bool = False
    DEPLOYMENT_REGION: str = "local"
    PORT: int = Field(default=5002, alias="AUTOPILOT_PORT")
    OPENAI_API_VERSION: str = "2024-06-01"
    ENABLE_TELEMETRY: bool = True
    REFRESH_EMBEDDINGS_INTERVAL: int = 90
    UIPATH_TOKEN: Optional[str] = None
    API_BUILD_VERSION: Optional[str] = "1.0.0"

    # Models
    AZURE_OPENAI_ENDPOINT: Optional[str] = None
    AZURE_OPENAI_API_KEY: Optional[str] = None
    AZURE_AI_STUDIO_API_KEY: Optional[str] = None
    USE_LLM_GATEWAY: bool = True

    # S2S
    S2S_CLIENT_ID: Optional[str] = "65E61F8C-F740-4A83-B2FF-EBE91EFE7AE8"
    S2S_CLIENT_SECRET: Optional[str] = "loaded-from-env"

    # Validators
    # pylint: disable=no-self-argument
    @field_validator("IS_PROD", mode="before")
    def set_is_prod(cls, value: Optional[bool], info: ValidationInfo) -> bool:
        if value is None:
            return info.data.get("CLOUD_URL_BASE") == "https://cloud.uipath.com"
        return value


settings = Settings()

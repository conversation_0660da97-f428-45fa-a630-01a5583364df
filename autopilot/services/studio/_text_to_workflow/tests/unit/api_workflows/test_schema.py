import pytest

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.schema import build_custom_workflow_model
from services.studio._text_to_workflow.common.schema import ActivityDefinition

SCOPE_PROPERTY_MAPS: dict[str, list[str]] = {
    "Catch": ["do"],
    "If": ["then", "else"],
    "ForEach": ["do"],
    "Sequence": ["do"],
    "TryCatch": ["try"],
}

STANDARD_SCHEMA_PROPERTIES = {
    "#/$defs/Response",
    "#/$defs/HttpRequest",
    "#/$defs/JsInvoke",
    "#/$defs/Catch",
    "#/$defs/Sequence",
    "#/$defs/ForEach",
    "#/$defs/If",
    "#/$defs/TryCatch",
}


@pytest.mark.parametrize(
    "activity_names",
    [
        [
            "UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_TeamsSend_Group_Chat_Message",
            "UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceUpdate_Account",
        ]
    ],
)
def test_build_custom_workflow_model(api_activities_retriever: APIActivitiesRetriever, activity_names: list[str]):
    """Test that a custom workflow model can be built from an activity definition."""
    # Get activity definitions from the retriever
    activity_defs = [actdef for activity_name in activity_names if (actdef := api_activities_retriever.get(activity_name, "activity")) is not None]

    # Build the custom workflow model
    custom_model = build_custom_workflow_model(activity_defs, 12)

    # Generate a JSON schema from the model
    json_schema = custom_model.model_json_schema()

    # Basic assertion to verify schema generation
    assert json_schema is not None
    assert "$defs" in json_schema
    for object_name, scope_properties in SCOPE_PROPERTY_MAPS.items():
        for scope_property in scope_properties:
            property_schema = json_schema["$defs"][object_name]["properties"][scope_property]
            assert property_schema is not None
            assert_schema_scope_properties(property_schema, activity_defs)


def assert_schema_scope_properties(property_schema: dict, activity_defs: list[ActivityDefinition]):
    """Assert that the scope property of the object is present in the JSON schema."""

    any_of_types = [t["$ref"] for t in property_schema["items"]["anyOf"]]
    # check that the standard schema properties are present
    for standard_schema_property in STANDARD_SCHEMA_PROPERTIES:
        assert standard_schema_property in any_of_types

    # check that the activity definitions are present
    for activity_def in activity_defs:
        assert "#/$defs/" + activity_def["className"] in any_of_types

    # check that other properties are not present
    assert len(any_of_types) == len(STANDARD_SCHEMA_PROPERTIES) + len(activity_defs)

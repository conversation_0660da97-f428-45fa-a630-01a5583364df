import asyncio
import copy
import json
import pathlib
import typing as t
from typing import cast

from langchain.output_parsers import YamlOutputParser
from langchain_core.prompts.chat import Chat<PERSON>romptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from services.studio._text_to_workflow.api_workflow.services.api_wf_activity_retrieval_service import APIActivityRetrievalService
from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import ApiWorkflowDraftResult
from services.studio._text_to_workflow.common.api_workflow.schema import (
    API_WF_EXPRESSION_LANGUAGE,
    ApiWorkflow,
    ApiWorkflowDataPoint,
    build_custom_workflow_model,
)
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, ActivityDefinition, Connection, OutputType
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.models.output_parsers import MinifiedYamlOutputParser
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.activity_utils import remove_dynamic_activity_prefix_from_name
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.json_utils import json_load_from_path
from services.studio._text_to_workflow.utils.paths import (
    get_api_workflow_retriever_dataset_path,
    get_api_workflow_static_retriever_dataset_path,
)
from services.studio._text_to_workflow.utils.request_utils import get_prompt_values_from_request_context
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.unidiff_utils import prepend_line_numbers
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.services.base_generator_service import BaseDraftGeneratorService
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import APIActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    WORKFLOW_PROCESSING_ERROR_SEVERITY,
    WorkflowProcessingErrorType,
)

LOGGER = AppInsightsLogger()

static_demos_path = get_api_workflow_static_retriever_dataset_path()

SIMPLE_DEMO_PATH = static_demos_path / "Updated Due Date of stale Asana Project.json"
COMPLEX_DEMO_PATH = static_demos_path / "Find Relevant Zendesk Ticket Comments and make them private.json"


class ApiWfDraftService(BaseDraftGeneratorService):
    draft_generation_model_name = "workflow_draft_generation_gemini_model"

    def __init__(
        self,
        activities_retriever: APIActivitiesRetriever,
        post_gen_processing_service: ApiWfPostGenerationProcessingService,
    ):
        # Config path for API workflow draft service
        config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "api_draft_creation_prompt.yaml"
        self.prompt_config = yaml_load(config_path)
        self.token_config = self.prompt_config["token_config"]  # token size settings for the prompt
        self.model_config = self.prompt_config["model_config"]  # model related settings for the generation, such as retry count, etc.
        # Initialize the base class with the token configuration
        super().__init__(
            model_name=self.draft_generation_model_name,
            consuming_feature_type=ConsumingFeatureType.WORKFLOW_GENERATION,
            retry_count=3,
        )

        self.activities_retriever = activities_retriever
        self.demonstration_output_parser = YamlOutputParser(pydantic_object=ApiWorkflow)
        self.post_gen_processing_service = post_gen_processing_service

        # load static demonstrations
        self.simple_demo = self._load_demonstration(SIMPLE_DEMO_PATH)
        self.complex_demo = self._load_demonstration(COMPLEX_DEMO_PATH)

    @staticmethod
    def get_current_level_error_count(severity_level: int, generation_errors: list[WorkflowProcessingErrorType]) -> int:
        current_category_count = 0
        for current_error in WORKFLOW_PROCESSING_ERROR_SEVERITY[severity_level]:
            current_category_count += generation_errors.count(current_error)
        return current_category_count

    @staticmethod
    def get_least_severe_error_trace_index(generated_answers: list[list[WorkflowProcessingErrorType]]) -> int:
        """
        For each severity level, we compute the minimum number of errors from that level for each generation.
        If there is a single generation with the minimum number of errors for a severity level, we return it.
        Otherwise, we move to the next severity level and repeat the process with the current level best generations.
        """
        error_severity_levels = WORKFLOW_PROCESSING_ERROR_SEVERITY.keys()
        active_generation_indices = range(len(generated_answers))
        for severity_level in error_severity_levels:
            best_trace = None
            for current_index in active_generation_indices:
                current_level_error_count = ApiWfDraftService.get_current_level_error_count(severity_level, generated_answers[current_index])
                if best_trace is None or current_level_error_count < best_trace:
                    best_trace = current_level_error_count
            active_generation_indices = [
                index
                for index in active_generation_indices
                if ApiWfDraftService.get_current_level_error_count(severity_level, generated_answers[index]) == best_trace
            ]

            if len(active_generation_indices) == 1:
                return active_generation_indices[0]

        return active_generation_indices[0]

    async def generate_workflow_draft(
        self,
        query: str,
        workflow: t.Optional[ApiWorkflow],
        connections: list[Connection],
        expression_language: API_WF_EXPRESSION_LANGUAGE,
        activity_retrieval_result: APIActivityRetrievalResult,
        mode: ActivitiesGenerationMode,  # should be "workflow" or "edit".
        retry_count: int | None = None,  # optionally, override the default retry count
    ) -> ApiWorkflowDraftResult:
        model = self._initialize_model(
            query,
            self.token_config["completion_tokens_min_bound"],
            self.token_config["completion_tokens_max_bound"],
            self.token_config["token_per_query_character"],
        )

        # make sure we're not using the wrong mode when presenting/prompting the model
        #  "workflow" will generate a new workflow or do a full rewrite.
        #  "edit" will use skip to the good bit and only generate local changes to the existing workflow.
        if mode != "workflow" and mode != "edit":
            raise ValueError("Invalid API Workflow Generation mode. Must be 'workflow' or 'edit'")

        if workflow is None and mode == "edit":
            raise ValueError("Edit mode requires an existing workflow")

        activity_defs = [
            act
            for act in (self.activities_retriever.get(a, "activity") for a in activity_retrieval_result.retrieved_activities)
            # generic dynamic activities are not supported for now, we rely exclusively on DAP HTTP Activities
            if act is not None
        ]

        connections_by_key = {conn["connector"]: conn for conn in connections if "connector" in conn}

        # TO DO: for now we need to call the DAP CLI to get the JSON Schema for the dynamic activities, in the future it should be cached
        augmented_type_defs_task = WorkflowGenerationDynamicActivitiesComponent.augment_type_definitions_for_dynamic_activities(
            {activity["fullClassName"]: activity for activity in activity_defs},
            {},
            connections_by_key,
            force_is_package_name=constants.FORCED_IS_PACKAGE_NAME,
            force_is_package_version=constants.FORCED_IS_PACKAGE_VERSION,
        )

        chat_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(self.prompt_config["system_msg"]),
                HumanMessagePromptTemplate.from_template(self.prompt_config["user_msg_template"]),
            ]
        )

        output_type_definitions = self.build_output_schema_definiton_prompt(activity_defs)

        # serialize the existing workflow
        serialized_workflow = workflow.yaml() if workflow else ""
        prompt_formatted_workflow = prepend_line_numbers(serialized_workflow) if mode == "edit" else serialized_workflow
        existing_workflow_prompt = (
            self.prompt_config["existing_workflow_template"].format(existing_workflow=prompt_formatted_workflow) if serialized_workflow else ""
        )

        # get format instructions with a dynamic JSON schema for the current proposed activities
        api_workflow_model = build_custom_workflow_model(activity_defs, self.prompt_config["schema"]["non_default_properties_limit"])
        format_instructions = MinifiedYamlOutputParser(pydantic_object=api_workflow_model).get_format_instructions()
        prompt_values = (
            {
                "query": query,
                "demo_examples": "",  # will be populated later
                "format_instructions": format_instructions,
                "output_type_definition": output_type_definitions,
                "simple_demo": self.serialize_demonstration(self.simple_demo, self.prompt_config["demonstration_template"]),
                "complex_demo": self.serialize_demonstration(self.complex_demo, self.prompt_config["demonstration_template"]),
                "existing_workflow_template": existing_workflow_prompt,
            }
            | get_prompt_values_from_request_context()
            | self._get_flair_specific_prompt_values(mode, self.prompt_config["flair_specific_sections"])
            | self.prompt_config[expression_language]  # get specific prompt values based on the expression language
        )
        # determine how many activity demos we can fit and add them in the prompt
        available_tokens = model.max_total_tokens - model.max_model_tokens  # type: ignore

        max_demonstrations_no = (
            self.prompt_config["demonstrations"]["max_demonstrations_for_skip_to_the_good_bit_edit"]
            if mode == "edit"
            else self.prompt_config["demonstrations"]["max_demonstrations"]
        )
        prompt_values["demo_examples"] = "\n".join(
            self._get_formatted_demonstrations(
                model,
                activity_retrieval_result.demonstrations[:max_demonstrations_no],
                self.prompt_config["demonstration_template"],
                available_tokens - model.get_num_tokens(chat_prompt.format(**prompt_values)),
            )
        )
        prompt_value = chat_prompt.invoke(prompt_values)

        try:
            config_map = None
            retry_no = retry_count if retry_count is not None else self.model_config["retry_count"]
            generated_answers = []
            generation_errors = []
            for i in range(retry_no):
                try:
                    if i > 0:
                        # TODO: Refactor this bit
                        LOGGER.info(f"Retrying API workflow draft generation for the {i} time")
                        query += "\nThink deeply about the query."

                    raw_model_prediction, token_usage = await self._get_model_response(
                        query,
                        model,
                        prompt_value,
                        self.token_config["completion_tokens_max_bound_for_overflow_retry"],
                    )

                    if config_map is None:
                        _, config_map = await augmented_type_defs_task

                    if mode == "edit":
                        workflow_raw_yaml, edit_patch_structure_errors = self._apply_diff_to_workflow(raw_model_prediction, serialized_workflow)
                    else:
                        workflow_raw_yaml = raw_model_prediction
                        edit_patch_structure_errors = []

                    # validate/correct the raw workflow and extract any errors
                    api_workflow_details = await self.post_gen_processing_service.process_raw_workflow(
                        raw_workflow=workflow_raw_yaml,
                        model=model,
                        config_map=config_map,
                        expression_language=expression_language,
                        connections_by_key=connections_by_key,
                        activity_definitions=activity_defs,
                        output_type_definitions=output_type_definitions,
                    )

                    all_errors: list[WorkflowProcessingErrorType] = copy.deepcopy(cast(list[WorkflowProcessingErrorType], edit_patch_structure_errors))

                    # we consider anything that isn't an invalid connection error as a fatal error that renders the workflow unusable
                    invalid_generation_errors = [
                        error.type
                        for error in api_workflow_details.post_generation_errors
                        if error.type != WorkflowProcessingErrorType.MISSING_OR_INVALID_CONNECTION
                    ]

                    all_errors.extend(invalid_generation_errors)

                    generation_errors.append(all_errors)

                    generated_answers.append(
                        ApiWorkflowDraftResult(
                            api_workflow_details=api_workflow_details,
                            prompt_value=prompt_value,
                            token_usage=token_usage,
                            activity_defs=activity_defs,
                            raw_model_prediction=workflow_raw_yaml,
                            edit_patch_structure_errors=edit_patch_structure_errors,
                        )
                    )

                    if i < retry_no - 1 and len(all_errors) > 0:
                        # if we are not on the last retry and there are errors, we can try again
                        continue
                    elif i == self.model_config["retry_count"] - 1 and len(all_errors) > 0:
                        raise Exception("No valid generation found")

                    return ApiWorkflowDraftResult(
                        api_workflow_details=api_workflow_details,
                        prompt_value=prompt_value,
                        token_usage=token_usage,
                        activity_defs=activity_defs,
                        raw_model_prediction=raw_model_prediction,
                        edit_patch_structure_errors=edit_patch_structure_errors,
                    )
                except Exception:
                    if i == retry_no - 1:
                        returned_generation = ApiWfDraftService.get_least_severe_error_trace_index(generation_errors)
                        return generated_answers[returned_generation]

            # If we've exhausted all retries and haven't returned yet, choose the best attempt
            if generated_answers:
                returned_generation = ApiWfDraftService.get_least_severe_error_trace_index(generation_errors)
                return generated_answers[returned_generation]

            # If we have no answers at all, we have to raise an exception
            raise Exception("Failed to generate a valid workflow after all retries")

        except Exception as e:
            augmented_type_defs_task.close()  # type: ignore
            raise e

    def build_output_schema_definiton_prompt(
        self,
        activity_defs: list[ActivityDefinition],
    ) -> str:
        activity_type_definitions = []
        api_integration_activity_definitions = []

        for activity in activity_defs:
            activity_name = remove_dynamic_activity_prefix_from_name(activity["fullClassName"])
            if activity["fullActivityName"] == constants.DAP_HTTP_ACTIVITY_NAME:
                api_integration_activity_definitions.append(
                    self.prompt_config["api_integration_activity_template"].format(
                        activity_name=activity_name,
                        category=activity.get("package_display_name", activity["category"]),
                    )
                )
                continue

            if activity["outputTypeJsonSchema"] is None:
                continue

            output_type: OutputType = activity["outputTypeJsonSchema"]
            # if the output type is an array, we need to use the array template
            template = self.prompt_config["output_array_type_definition_template" if output_type["isArray"] else "output_type_definition_template"]

            # determine the name of a property of the output type we can use as an example
            output_type_name = output_type["type"]
            root_type = output_type["definitions"][output_type_name]
            # for simplicity, chose the first string property
            example_property = next((prop for prop, def_prop in root_type["properties"].items() if "type" in def_prop and def_prop["type"] == "string"), None)

            if example_property is None:
                # as a fallback, chose the first non-metadata property
                example_property = next((prop for prop in root_type["properties"] if prop != "_metadata"), None)

                if example_property is None:
                    raise ValueError(f"No valid property found in activity schema for activity {activity_name}")

            activity_type_definitions.append(
                template.format(
                    activity_name=activity_name,
                    type=output_type_name,
                    definition=json.dumps(output_type["definitions"], separators=(",", ":")),  # separators help minify the json string
                    property_name=example_property,
                )
            )

        api_integration_activities_section_template = (
            self.prompt_config["api_integration_activities_section_template"].format(
                output_api_integration_activities="\n".join(api_integration_activity_definitions)
            )
            if len(api_integration_activity_definitions) > 0
            else ""
        )

        output_type_definitions_section_template = (
            self.prompt_config["output_type_definitions_section_template"].format(output_type_definitions="\n".join(activity_type_definitions))
            if len(activity_type_definitions) > 0
            else ""
        )

        return output_type_definitions_section_template + api_integration_activities_section_template

    def serialize_demonstration(self, demonstration: ApiWorkflowDataPoint, prompt_template: str, **serialization_kwargs) -> str:
        return prompt_template.format(query=demonstration.query, workflow=demonstration.solution_workflow.yaml())

    @staticmethod
    def _load_demonstration(path: pathlib.Path) -> ApiWorkflowDataPoint:
        """Load a demonstration from a JSON file and convert it to an ApiWorkflowDataPoint."""
        with path.open("r") as f:
            demo = json.load(f)
        return ApiWorkflowDataPoint.model_validate(demo)


if __name__ == "__main__":

    def load_api_workflow_datapoints() -> list[ApiWorkflowDataPoint]:
        dataset_dir = get_api_workflow_retriever_dataset_path()

        # Find all JSON files in the dataset directory
        api_wf_files = list(dataset_dir.rglob("*.json"))

        datapoints = []
        for file_path in api_wf_files:
            # Load the JSON file
            data = json_load_from_path(file_path)

            # Convert to ApiWorkflowDataPoint
            datapoint = ApiWorkflowDataPoint.model_validate(data)
            datapoints.append(datapoint)

        print(f"Loaded {len(datapoints)} API workflow datapoints")
        return datapoints

    activitiesFetcher = APIActivitiesRetriever()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activitiesFetcher, embedding_model)

    activitiesRetrievalService = APIActivityRetrievalService(activitiesFetcher, connection_embeddings_retriever)

    post_gen_processing_service = ApiWfPostGenerationProcessingService(activitiesFetcher)
    draftCreationService = ApiWfDraftService(activitiesFetcher, post_gen_processing_service)

    tenant_id, default_connections = get_connections_data()

    request_context = get_testing_request_context("en", tenant_id, "Workflow Dataset Generation")
    request_utils.set_request_context(request_context)

    # Load the API workflow datapoints
    retrieved_demonstrations = [dp for dp in load_api_workflow_datapoints() if dp.query]

    for demo in retrieved_demonstrations:
        try:
            activity_retrieval_result: APIActivityRetrievalResult = asyncio.run(
                activitiesRetrievalService.generate_relevant_activities(demo.query, demo.existing_workflow, default_connections, True)
            )
            draft = asyncio.run(
                draftCreationService.generate_workflow_draft(
                    query=demo.query,
                    workflow=demo.existing_workflow,
                    connections=default_connections,
                    expression_language=demo.language,
                    activity_retrieval_result=activity_retrieval_result,
                    mode=demo.mode,
                )
            )
            print(draft)
        except Exception as e:
            print(f"❌ Error generating workflow draft for {demo.query}: {e}")
            continue

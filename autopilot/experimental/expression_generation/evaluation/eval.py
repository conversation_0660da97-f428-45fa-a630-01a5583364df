import asyncio

import <PERSON><PERSON><PERSON><PERSON>
from eval_api_fix_expression import examples as api_fix_expression_examples
from eval_api_generation import examples as api_gen_examples
from eval_cases import examples as expression_gen_examples
from eval_cases_apps import app_examples
from eval_cases_js_codes import examples as js_invoke_examples
from eval_cases_js_expressions import examples as js_examples
from eval_fix_codes_js import examples as js_invoke_fix_examples
from eval_fix_expression_cases import examples as fix_expression_gen_examples
from eval_fix_expression_js import examples as fix_expression_gen_js_examples
from termcolor import colored

from experimental.expression_generation.evaluation.expression_generation_eval_traces_schema import (
    ExpressionGenerationInputSchema,
    ExpressionGenerationOutputSchema,
    FixGenerationInputSchema,
)
from services.studio._text_to_workflow.expression_generation import expression_generation_endpoint
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load


def color_print(text, color):
    coloredText = colored(text, color)
    print(coloredText)


token = ""


async def eval_examples(examples, eval_set_run_name, type):
    evals = []
    for i in range(len(examples)):
        try:
            input = examples[i]

            if type == "Expression":
                response = await expression_generation_endpoint.generate(input)
                name = input["userRequest"]
            elif type == "Fix":
                response = await expression_generation_endpoint.fix_expression(input)
                name = input["currentExpression"]

            result = yaml_load(response["result"])
            result_expression = result["expression"]
            benchmark_expression = input["benchmarkExpression"]
            distance = Levenshtein.ratio(result_expression, benchmark_expression)

            if distance < 0.85:
                color_print("Wrong answer for example: {}".format(name), "red")
                color_print("Expected: {}. Actual: {}".format(input["benchmarkExpression"], result_expression), "red")
                color_print(distance, "red")

            else:
                color_print("Passed TC for : {}".format(name), "green")
                color_print("Expected: {}. Actual: {}".format(input["benchmarkExpression"], result_expression), "green")
                color_print(distance, "green")
            input["availableVariables"] = str(input["availableVariables"])
            if "id" in input:
                id = input["id"]
                del input["id"]
            else:
                id = "1"
            eval = {
                "id": id,
                "name": name,
                "input": input,
                "output_key": "result",
                "result": {
                    "output": {"result": result_expression, "benchmark_expression": benchmark_expression},
                    "score": {"type": "number", "value": distance},
                },
                "trace": {
                    "attributes": {"model_name": ModelManager().get_llm_model("expression_generation_model", ConsumingFeatureType.EXPRESSION_GENERATION).name},
                    "name": f"{eval_set_run_name} - trace {name}",
                },
            }
            del eval["input"]["benchmarkExpression"]
            evals.append(eval)

        except Exception as e:
            color_print("Exception for example: {}".format(input.get("userRequest", input.get("name"))), "red")
            color_print(e, "red")

    eval_set_run = {"name": eval_set_run_name, "evals": evals, "output_schema": ExpressionGenerationOutputSchema}
    if type == "Expression":
        eval_set_run["input_schema"] = ExpressionGenerationInputSchema
    elif type == "Fix":
        eval_set_run["input_schema"] = FixGenerationInputSchema
    return eval_set_run


async def main():
    await eval_examples(js_invoke_examples, "Eval js invoke generation", "Expression")

    await eval_examples(js_invoke_fix_examples, "Eval js invoke fix expression", "Fix")

    await eval_examples(api_gen_examples, "Eval expression generation", "Expression")

    await eval_examples(api_fix_expression_examples, "Eval fix expression", "Fix")

    await eval_examples(fix_expression_gen_js_examples, "Eval fix expression js", "Fix")

    await eval_examples(js_examples, "Eval expression generation", "Expression")

    await eval_examples(expression_gen_examples, "Eval expression generation", "Expression")

    await eval_examples(app_examples, "Eval apps expression generation", "Expression")

    await eval_examples(fix_expression_gen_examples, "Eval fix expression", "Fix")


if __name__ == "__main__":
    asyncio.run(main())

basic_api_workflow:
  query: "Using the 'IssueName' and 'IssueDescription' variables, create a new GithubIncident and subsequently a corresponding ServiceNow Incident. Make sure the description of the Incident references the Github Issue URL"
  mode: "workflow"
  retrieved_activities:
    - UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRChange_Time_off_Request_Status
    - UiPath.IntegrationService.Activities.Runtime.Activities.ServiceNowCreate_New_Incident
    - UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_User
    - UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_TeamsSend_Channel_Message
    - UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRGet_Time_off_Requests
    - UiPath.IntegrationService.Activities.Runtime.Activities.GitHubCreate_Issue
  expected_solution_activities: # the activities (in the order) that should be present in the solution
    - GitHubCreate_Issue
    - ServiceNowCreate_New_Incident
  existing_workflow: null
api_integration_activity_workflow:
  query: Get via an HTTP Request the Github commits made by a specific user to a specific repository.
  mode: "workflow"
  retrieved_activities:
    - UiPath.IntegrationService.Activities.Runtime.Activities.GitHub_HTTP_Request
  expected_solution_activities:
    - GitHub_HTTP_Request
  existing_workflow: null
response_activity_workflow:
  query: Get the latest mail from the user's inbox using Outlook and respond with a json object with the subject, sender, and body of the email.
  mode: "workflow"
  retrieved_activities:
    - UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_Outlook_365Get_Email_By_ID
    - UiPath.IntegrationService.Activities.Runtime.Activities.AweberGet_Subscriber_by_Email
    - UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_Outlook_365List_All_Records
    - UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_Outlook_365Get_Newest_Email
    - UiPath.IntegrationService.Activities.Runtime.Activities.PDFMonkeyPDFMonkey_HTTP_Request
    - UiPath.IntegrationService.Activities.Runtime.Activities.PDFMonkeyGet_Record
    - UiPath.IntegrationService.Activities.Runtime.Activities.AweberAweber_HTTP_Request
  expected_solution_activities:
    - Microsoft_Outlook_365Get_Newest_Email
    - Response
  existing_workflow: null
basic_skip_to_the_good_bit_edit_scenario:
  query: Get the corresponding Salesforce Account using the AccountId from the contact details and send a Slack message to 'SlackUserEmail' with the contact details and Account name
  mode: "edit"
  retrieved_activities:
    - UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceGet_Contact
    - UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceGet_Account
    - UiPath.IntegrationService.Activities.Runtime.Activities.Slack_HTTP_Request
    - UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_User
  expected_solution_activities:
    - SalesforceGet_Contact
    - SalesforceGet_Account
    - SlackSend_Message_to_User
  existing_workflow: |
    {
      "input": "{\"type\": \"object\", \"properties\": {\"EmailAddress\": {\"type\": \"string\"}}, \"required\": [\"EmailAddress\"]}",
      "root": {
        "thought": "Sequence",
        "activity": "Sequence",
        "id": "Sequence_1",
        "do": [
          {
            "thought": "List All Tasks in Workday Inbox",
            "activity": "Workday_RESTList_All_Tasks_in_Your_Inbox",
            "id": "me_inboxTasks_1",
            "with": {}
          }
        ]
      }
    }
    
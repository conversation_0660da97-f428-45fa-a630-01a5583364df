{"UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRChange_Time_off_Request_Status": {"activityTypeId": "93d7da58-c901-3e60-be55-14860918bafd", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "40c2218d-cba4-44bf-a134-5c0cde0ce432", "configuration": "dummyConfig", "connectorKey": "uipath-bamboo-bamboohr", "activityIsConfigured": true, "inputPropertiesJsonSchema": [{"name": "start_date", "schema_name": "start_date", "type": "string", "required": true, "location": "Body"}, {"name": "end_date", "schema_name": "end_date", "type": "string", "required": true, "location": "Body"}, {"name": "id", "schema_name": "id", "type": "string", "required": true, "location": "Path"}, {"name": "status", "schema_name": "status", "type": "string", "required": false, "location": "Body"}, {"name": "note", "schema_name": "note", "type": "string", "required": false, "location": "Body"}], "outputTypeJsonSchema": {"type": "update_time_off_status_Replace", "definitions": {"update_time_off_status_Replace": {"type": "object", "properties": {"code": {"type": "integer"}, "status": {"type": "string"}}}}, "isArray": false}, "httpMethod": "PUT"}, "UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_User": {"activityTypeId": "9a76a063-8903-3101-b158-e0435e77c45e", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "917ef207-cebc-483b-beef-a2e6c41c1521", "connectorKey": "uipath-salesforce-slack", "configuration": "dummyConfig", "activityIsConfigured": true, "httpMethod": "POST", "inputPropertiesJsonSchema": [{"name": "channel", "schema_name": "channel", "type": "string", "required": true, "location": "Body"}, {"name": "messageToSend", "schema_name": "messageToSend", "type": "string", "required": true, "location": "Body"}, {"name": "send_as", "schema_name": "send_as", "type": "string", "required": true, "location": "Body"}, {"name": "fields", "schema_name": "fields", "type": "string", "required": false, "location": "Body"}, {"name": "buttons", "schema_name": "buttons", "type": "string", "required": false, "location": "Body"}, {"name": "image", "schema_name": "image", "type": "string", "required": false, "location": "Body"}, {"name": "parse", "schema_name": "parse", "type": "string", "required": false, "location": "Body"}, {"name": "link_names", "schema_name": "link_names", "type": "boolean", "required": false, "location": "Body"}, {"name": "unfurl_links", "schema_name": "unfurl_links", "type": "boolean", "required": false, "location": "Body"}, {"name": "username", "schema_name": "username", "type": "string", "required": false, "location": "Body"}, {"name": "icon_emoji", "schema_name": "icon_emoji", "type": "string", "required": false, "location": "Body"}], "outputTypeJsonSchema": {"type": "send_message_to_user_v2_Create", "definitions": {"icons": {"type": "object", "properties": {"emoji": {"type": "string"}}}, "icons1": {"type": "object", "properties": {"image_36": {"type": "string"}, "image_48": {"type": "string"}, "image_72": {"type": "string"}}}, "bot_profile": {"type": "object", "properties": {"icons": {"$ref": "#/definitions/icons1"}, "app_id": {"type": "string"}, "deleted": {"type": "boolean"}, "id": {"type": "string"}, "name": {"type": "string"}, "team_id": {"type": "string"}, "updated": {"type": "integer"}}}, "event_payload": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}}}, "metadata": {"type": "object", "properties": {"event_payload": {"$ref": "#/definitions/event_payload"}, "event_type": {"type": "string"}}}, "metadata1": {"type": "object", "properties": {"event_type": {"type": "string"}}}, "root": {"type": "object", "properties": {"blocks": {"type": "object"}, "icons": {"$ref": "#/definitions/icons"}, "metadata": {"$ref": "#/definitions/metadata1"}, "app_id": {"type": "string"}, "bot_id": {"type": "string"}, "is_locked": {"type": "boolean"}, "latest_reply": {"type": "string"}, "reply_count": {"type": "integer"}, "reply_users": {"type": "object"}, "reply_users_count": {"type": "integer"}, "subscribed": {"type": "boolean"}, "subtype": {"type": "string"}, "text": {"type": "string"}, "thread_ts": {"type": "string"}, "ts": {"type": "string"}, "type": {"type": "string"}, "username": {"type": "string"}}}, "message": {"type": "object", "properties": {"attachments": {"type": "object"}, "blocks": {"type": "object"}, "bot_profile": {"$ref": "#/definitions/bot_profile"}, "icons": {"$ref": "#/definitions/icons"}, "metadata": {"$ref": "#/definitions/metadata"}, "root": {"$ref": "#/definitions/root"}, "app_id": {"type": "string"}, "bot_id": {"type": "string"}, "subtype": {"type": "string"}, "team": {"type": "string"}, "text": {"type": "string"}, "thread_ts": {"type": "string"}, "ts": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}, "username": {"type": "string"}}}, "response_metadata": {"type": "object", "properties": {"warnings": {"type": "object"}}}, "send_message_to_user_v2_Create": {"type": "object", "properties": {"blocks": {"type": "object"}, "icons": {"$ref": "#/definitions/icons"}, "message": {"$ref": "#/definitions/message"}, "metadata": {"$ref": "#/definitions/metadata"}, "response_metadata": {"$ref": "#/definitions/response_metadata"}, "root": {"$ref": "#/definitions/root"}, "app_id": {"type": "string"}, "channel": {"type": "string"}, "ok": {"type": "boolean"}, "subtype": {"type": "string"}, "thread_ts": {"type": "string"}, "ts": {"type": "string"}, "username": {"type": "string"}}}}, "isArray": false}}, "UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRGet_Time_off_Requests": {"activityTypeId": "468d10a7-5d93-3cca-98fc-a319dda2b118", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "40c2218d-cba4-44bf-a134-5c0cde0ce432", "connectorKey": "uipath-bamboo-bamboohr", "configuration": "dummyConfig", "activityIsConfigured": true, "httpMethod": "GET", "inputPropertiesJsonSchema": [{"name": "start", "schema_name": "start", "type": "string", "required": true, "location": "Query"}, {"name": "end", "schema_name": "end", "type": "string", "required": false, "location": "Query"}, {"name": "employeeId", "schema_name": "employeeId", "type": "string", "required": false, "location": "Query"}, {"name": "type", "schema_name": "type", "type": "string", "required": false, "location": "Query"}, {"name": "timeOffStatus", "schema_name": "timeOffStatus", "type": "string", "required": false, "location": "Query"}], "outputTypeJsonSchema": {"type": "get_time_off_requests_List", "definitions": {"actions": {"type": "object", "properties": {"approve": {"type": "boolean"}, "bypass": {"type": "boolean"}, "cancel": {"type": "boolean"}, "deny": {"type": "boolean"}, "edit": {"type": "boolean"}, "view": {"type": "boolean"}}}, "amount": {"type": "object", "properties": {"_amount": {"type": "string"}, "unit": {"type": "string"}}}, "notes": {"type": "object", "properties": {"manager": {"type": "string"}, "employee": {"type": "string"}}}, "status": {"type": "object", "properties": {"lastChanged": {"type": "string", "format": "date-time"}, "lastChangedByUserId": {"type": "string"}, "_status": {"type": "string"}}}, "type": {"type": "object", "properties": {"icon": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "get_time_off_requests_List": {"type": "object", "properties": {"actions": {"$ref": "#/definitions/actions"}, "amount": {"$ref": "#/definitions/amount"}, "notes": {"$ref": "#/definitions/notes"}, "status": {"$ref": "#/definitions/status"}, "type": {"$ref": "#/definitions/type"}, "created": {"type": "string", "format": "date-time"}, "employeeId": {"type": "string"}, "_end": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "name": {"type": "string"}, "start": {"type": "string", "format": "date-time"}}}}, "isArray": true}}, "UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_TeamsSend_Channel_Message": {"activityTypeId": "5db80592-ff66-39b4-b0f2-20bc9d93b7e4", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "24005cd4-1ad9-4e71-85ea-47d55cd2d5de", "connectorKey": "uipath-microsoft-teams", "activityIsConfigured": true, "configuration": "dummyConfig", "httpMethod": "POST", "inputPropertiesJsonSchema": [{"name": "team_id", "schema_name": "team_id", "type": "string", "required": true, "location": "Path"}, {"name": "channel_id", "schema_name": "channel_id", "type": "string", "required": true, "location": "Path"}, {"name": "body_sub_content", "schema_name": "body_sub_content", "type": "string", "required": false, "location": "Body"}, {"name": "body_sub_adaptiveCardContent", "schema_name": "body_sub_adaptiveCardContent", "type": "string", "required": false, "location": "Body"}, {"name": "file", "schema_name": "file", "type": "string", "required": false, "location": "Multipart"}, {"name": "attachmentIds_array", "schema_name": "attachmentIds_array", "type": "array", "required": false, "location": "Body"}], "outputTypeJsonSchema": {"type": "normalisedminus_signteams_channels_messagesminus_signv2_Create", "definitions": {"body": {"type": "object", "properties": {"content": {"type": "string"}}}, "channelIdentity": {"type": "object", "properties": {"channelId": {"type": "string"}, "teamId": {"type": "string"}}}, "user": {"type": "object", "properties": {"displayName": {"type": "string"}, "id": {"type": "string"}, "userIdentityType": {"type": "string"}}}, "from": {"type": "object", "properties": {"user": {"$ref": "#/definitions/user"}}}, "mentioned": {"type": "object", "properties": {"user": {"$ref": "#/definitions/mentioned"}}}, "mentions": {"type": "object", "properties": {"mentioned": {"$ref": "#/definitions/mentioned"}, "id": {"type": "integer"}, "mentionText": {"type": "string"}}}, "normalisedminus_signteams_channels_messagesminus_signv2_Create": {"type": "object", "properties": {"body": {"$ref": "#/definitions/body"}, "channelIdentity": {"$ref": "#/definitions/channelIdentity"}, "from": {"$ref": "#/definitions/from"}, "mentions": {"$ref": "#/definitions/mentions"}, "createdDateTime": {"type": "string", "format": "date-time"}, "etag": {"type": "string"}, "id": {"type": "string"}, "importance": {"type": "string"}, "lastModifiedDateTime": {"type": "string", "format": "date-time"}, "locale": {"type": "string"}, "messageType": {"type": "string"}, "odataContext": {"type": "string"}, "webUrl": {"type": "string"}}}}, "isArray": false}}, "UiPath.IntegrationService.Activities.Runtime.Activities.ServiceNowCreate_New_Incident": {"activityTypeId": "06ca824f-f978-3008-a925-dee245fc6614", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "fb2fd627-5f25-4e0c-97c2-3e757df1b33b", "connectorKey": "uipath-servicenow-servicenow", "activityIsConfigured": true, "configuration": "dummyConfig", "httpMethod": "POST", "inputPropertiesJsonSchema": [{"name": "caller_id", "schema_name": "caller_id", "type": "string", "required": false, "location": "Body"}, {"name": "short_description", "schema_name": "short_description", "type": "string", "required": false, "location": "Body"}, {"name": "description", "schema_name": "description", "type": "string", "required": false, "location": "Body"}, {"name": "urgency", "schema_name": "urgency", "type": "integer", "required": false, "location": "Body"}, {"name": "impact", "schema_name": "impact", "type": "integer", "required": false, "location": "Body"}, {"name": "assigned_to", "schema_name": "assigned_to", "type": "string", "required": false, "location": "Body"}], "outputTypeJsonSchema": {"type": "curated_incident_Create", "definitions": {"curated_incident_Create": {"type": "object", "properties": {"caller_id_link": {"type": "string"}, "caller_id": {"type": "string"}, "opened_at": {"type": "string", "format": "date-time"}, "urgency": {"type": "integer"}, "short_description": {"type": "string"}, "state": {"type": "integer"}, "sys_id": {"type": "string"}, "closed_at": {"type": "string", "format": "date-time"}, "close_code": {"type": "string"}, "close_notes": {"type": "string"}, "assigned_to": {"type": "string"}, "description": {"type": "string"}, "impact": {"type": "integer"}, "number": {"type": "string"}, "parent": {"type": "string"}, "caused_by": {"type": "string"}, "watch_list": {"type": "string"}, "upon_reject": {"type": "string"}, "sys_updated_on": {"type": "string", "format": "date-time"}, "origin_table": {"type": "string"}, "approval_history": {"type": "string"}, "parent_link": {"type": "string"}, "skills": {"type": "string"}, "closed_by_link": {"type": "string"}, "caused_by_link": {"type": "string"}, "sys_created_by": {"type": "string"}, "knowledge": {"type": "string"}, "order": {"type": "integer"}, "cmdb_ci": {"type": "string"}, "delivery_plan": {"type": "string"}, "sn_fin_accounting_period_link": {"type": "string"}, "contract": {"type": "string"}, "active": {"type": "string"}, "work_notes_list": {"type": "string"}, "opened_by_link": {"type": "string"}, "priority": {"type": "integer"}, "sys_domain_path": {"type": "string"}, "rejection_goto": {"type": "string"}, "business_duration": {"type": "string"}, "group_list": {"type": "string"}, "parent_incident_link": {"type": "string"}, "approval_set": {"type": "string", "format": "date-time"}, "resolved_by_link": {"type": "string"}, "wf_activity": {"type": "string"}, "universal_request": {"type": "string"}, "sys_domain_link": {"type": "string"}, "correlation_display": {"type": "string"}, "delivery_task": {"type": "string"}, "work_start": {"type": "string", "format": "date-time"}, "company_link": {"type": "string"}, "delivery_task_link": {"type": "string"}, "additional_assignee_list": {"type": "string"}, "additional_assignee_list_link": {"type": "string"}, "notify": {"type": "integer"}, "service_offering": {"type": "string"}, "sys_class_name": {"type": "string"}, "closed_by": {"type": "string"}, "follow_up": {"type": "string", "format": "date-time"}, "parent_incident": {"type": "string"}, "reopened_by_link": {"type": "string"}, "reopened_by": {"type": "string"}, "group_list_link": {"type": "string"}, "reassignment_count": {"type": "integer"}, "variables": {"type": "string"}, "sla_due": {"type": "string"}, "universal_request_link": {"type": "string"}, "comments_and_work_notes": {"type": "string"}, "cmdb_ci_link": {"type": "string"}, "contract_link": {"type": "string"}, "escalation": {"type": "integer"}, "upon_approval": {"type": "string"}, "wf_activity_link": {"type": "string"}, "correlation_id": {"type": "string"}, "assignment_group_link": {"type": "string"}, "origin_id_link": {"type": "string"}, "made_sla": {"type": "string"}, "child_incidents": {"type": "integer"}, "hold_reason": {"type": "integer"}, "task_effective_number": {"type": "string"}, "resolved_by": {"type": "string"}, "sys_updated_by": {"type": "string"}, "opened_by": {"type": "string"}, "user_input": {"type": "string"}, "sys_created_on": {"type": "string", "format": "date-time"}, "sys_domain": {"type": "string"}, "skills_link": {"type": "string"}, "route_reason": {"type": "integer"}, "calendar_stc": {"type": "integer"}, "location_link": {"type": "string"}, "business_service": {"type": "string"}, "business_impact": {"type": "string"}, "rfc": {"type": "string"}, "time_worked": {"type": "string"}, "assigned_to_link": {"type": "string"}, "expected_start": {"type": "string", "format": "date-time"}, "work_end": {"type": "string", "format": "date-time"}, "reopened_time": {"type": "string", "format": "date-time"}, "resolved_at": {"type": "string", "format": "date-time"}, "subcategory": {"type": "string"}, "work_notes": {"type": "string"}, "watch_list_link": {"type": "string"}, "assignment_group": {"type": "string"}, "problem_id_link": {"type": "string"}, "business_stc": {"type": "integer"}, "cause": {"type": "string"}, "origin_id": {"type": "string"}, "calendar_duration": {"type": "string"}, "service_offering_link": {"type": "string"}, "business_service_link": {"type": "string"}, "contact_type": {"type": "string"}, "work_notes_list_link": {"type": "string"}, "incident_state": {"type": "integer"}, "rfc_link": {"type": "string"}, "problem_id": {"type": "string"}, "company": {"type": "string"}, "activity_due": {"type": "string"}, "severity": {"type": "integer"}, "comments": {"type": "string"}, "approval": {"type": "string"}, "due_date": {"type": "string", "format": "date-time"}, "sys_mod_count": {"type": "integer"}, "rejection_goto_link": {"type": "string"}, "reopen_count": {"type": "integer"}, "sn_fin_accounting_period": {"type": "string"}, "location": {"type": "string"}, "category": {"type": "string"}, "delivery_plan_link": {"type": "string"}}}}, "isArray": false}}, "UiPath.IntegrationService.Activities.Runtime.Activities.GitHubCreate_Issue": {"activityTypeId": "f7b2f478-d940-33c1-a56d-5d50e81afe2c", "actualActivityName": "UiPath.IntegrationService.Activities.Runtime.Activities.ConnectorActivity", "connectionId": "2e9e5860-7341-44e0-bf6a-1268581e6bb6", "connectorKey": "uipath-microsoft-github", "activityIsConfigured": true, "configuration": "dummyConfig", "httpMethod": "POST", "inputPropertiesJsonSchema": [{"name": "repo", "schema_name": "repo", "type": "string", "required": true, "location": "Path"}, {"name": "title", "schema_name": "title", "type": "string", "required": true, "location": "Body"}, {"name": "assignee_sub_login", "schema_name": "assignee_sub_login", "type": "string", "required": false, "location": "Body"}, {"name": "body", "schema_name": "body", "type": "string", "required": false, "location": "Body"}, {"name": "milestone_sub_number", "schema_name": "milestone_sub_number", "type": "integer", "required": false, "location": "Body"}], "outputTypeJsonSchema": {"type": "create_issues_Create", "definitions": {"assignee": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "email": {"type": "string"}, "events_url": {"type": "string"}, "followers_url": {"type": "string"}, "following_url": {"type": "string"}, "gists_url": {"type": "string"}, "gravatar_id": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "login": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "organizations_url": {"type": "string"}, "received_events_url": {"type": "string"}, "repos_url": {"type": "string"}, "site_admin": {"type": "boolean"}, "starred_at": {"type": "string"}, "starred_url": {"type": "string"}, "subscriptions_url": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "closed_by": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "email": {"type": "string"}, "events_url": {"type": "string"}, "followers_url": {"type": "string"}, "following_url": {"type": "string"}, "gists_url": {"type": "string"}, "gravatar_id": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "login": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "organizations_url": {"type": "string"}, "received_events_url": {"type": "string"}, "repos_url": {"type": "string"}, "site_admin": {"type": "boolean"}, "starred_at": {"type": "string"}, "starred_url": {"type": "string"}, "subscriptions_url": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "creator": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "email": {"type": "string"}, "events_url": {"type": "string"}, "followers_url": {"type": "string"}, "following_url": {"type": "string"}, "gists_url": {"type": "string"}, "gravatar_id": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "login": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "organizations_url": {"type": "string"}, "received_events_url": {"type": "string"}, "repos_url": {"type": "string"}, "site_admin": {"type": "boolean"}, "starred_at": {"type": "string"}, "starred_url": {"type": "string"}, "subscriptions_url": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "milestone": {"type": "object", "properties": {"creator": {"$ref": "#/definitions/creator"}, "closed_at": {"type": "string", "format": "date-time"}, "closed_issues": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "due_on": {"type": "string", "format": "date-time"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "labels_url": {"type": "string"}, "node_id": {"type": "string"}, "number": {"type": "integer"}, "open_issues": {"type": "integer"}, "state": {"type": "string"}, "title": {"type": "string"}, "updated_at": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}}, "owner": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "email": {"type": "string"}, "events_url": {"type": "string"}, "followers_url": {"type": "string"}, "following_url": {"type": "string"}, "gists_url": {"type": "string"}, "gravatar_id": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "login": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "organizations_url": {"type": "string"}, "received_events_url": {"type": "string"}, "repos_url": {"type": "string"}, "site_admin": {"type": "boolean"}, "starred_at": {"type": "string"}, "starred_url": {"type": "string"}, "subscriptions_url": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "permissions": {"type": "object", "properties": {"checks": {"type": "string"}, "contents": {"type": "string"}, "deployments": {"type": "string"}, "issues": {"type": "string"}, "metadata": {"type": "string"}}}, "performed_via_github_app": {"type": "object", "properties": {"owner": {"$ref": "#/definitions/owner"}, "permissions": {"$ref": "#/definitions/permissions"}, "client_id": {"type": "string"}, "client_secret": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "events": {"type": "object"}, "external_url": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "installations_count": {"type": "integer"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "pem": {"type": "string"}, "slug": {"type": "string"}, "updated_at": {"type": "string", "format": "date-time"}, "webhook_secret": {"type": "string"}}}, "pull_request": {"type": "object", "properties": {"diff_url": {"type": "string"}, "html_url": {"type": "string"}, "merged_at": {"type": "string", "format": "date-time"}, "patch_url": {"type": "string"}, "url": {"type": "string"}}}, "reactions": {"type": "object", "properties": {"plus_sign1": {"type": "integer"}, "minus_sign1": {"type": "integer"}, "confused": {"type": "integer"}, "eyes": {"type": "integer"}, "heart": {"type": "integer"}, "hooray": {"type": "integer"}, "laugh": {"type": "integer"}, "rocket": {"type": "integer"}, "total_count": {"type": "integer"}, "url": {"type": "string"}}}, "license": {"type": "object", "properties": {"html_url": {"type": "string"}, "key": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "spdx_id": {"type": "string"}, "url": {"type": "string"}}}, "organization": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "email": {"type": "string"}, "events_url": {"type": "string"}, "followers_url": {"type": "string"}, "following_url": {"type": "string"}, "gists_url": {"type": "string"}, "gravatar_id": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "login": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "organizations_url": {"type": "string"}, "received_events_url": {"type": "string"}, "repos_url": {"type": "string"}, "site_admin": {"type": "boolean"}, "starred_at": {"type": "string"}, "starred_url": {"type": "string"}, "subscriptions_url": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "permissions1": {"type": "object", "properties": {"admin": {"type": "boolean"}, "maintain": {"type": "boolean"}, "pull": {"type": "boolean"}, "push": {"type": "boolean"}, "triage": {"type": "boolean"}}}, "owner1": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "events_url": {"type": "string"}, "followers_url": {"type": "string"}, "following_url": {"type": "string"}, "gists_url": {"type": "string"}, "gravatar_id": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "login": {"type": "string"}, "node_id": {"type": "string"}, "organizations_url": {"type": "string"}, "received_events_url": {"type": "string"}, "repos_url": {"type": "string"}, "site_admin": {"type": "boolean"}, "starred_url": {"type": "string"}, "subscriptions_url": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "template_repository": {"type": "object", "properties": {"owner": {"$ref": "#/definitions/owner1"}, "permissions": {"$ref": "#/definitions/permissions1"}, "allow_auto_merge": {"type": "boolean"}, "allow_merge_commit": {"type": "boolean"}, "allow_rebase_merge": {"type": "boolean"}, "allow_squash_merge": {"type": "boolean"}, "allow_update_branch": {"type": "boolean"}, "archive_url": {"type": "string"}, "archived": {"type": "boolean"}, "assignees_url": {"type": "string"}, "blobs_url": {"type": "string"}, "branches_url": {"type": "string"}, "clone_url": {"type": "string"}, "collaborators_url": {"type": "string"}, "comments_url": {"type": "string"}, "commits_url": {"type": "string"}, "compare_url": {"type": "string"}, "contents_url": {"type": "string"}, "contributors_url": {"type": "string"}, "created_at": {"type": "string"}, "default_branch": {"type": "string"}, "delete_branch_on_merge": {"type": "boolean"}, "deployments_url": {"type": "string"}, "description": {"type": "string"}, "disabled": {"type": "boolean"}, "downloads_url": {"type": "string"}, "events_url": {"type": "string"}, "fork": {"type": "boolean"}, "forks_count": {"type": "integer"}, "forks_url": {"type": "string"}, "full_name": {"type": "string"}, "git_commits_url": {"type": "string"}, "git_refs_url": {"type": "string"}, "git_tags_url": {"type": "string"}, "git_url": {"type": "string"}, "has_downloads": {"type": "boolean"}, "has_issues": {"type": "boolean"}, "has_pages": {"type": "boolean"}, "has_projects": {"type": "boolean"}, "has_wiki": {"type": "boolean"}, "homepage": {"type": "string"}, "hooks_url": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "is_template": {"type": "boolean"}, "issue_comment_url": {"type": "string"}, "issue_events_url": {"type": "string"}, "issues_url": {"type": "string"}, "keys_url": {"type": "string"}, "labels_url": {"type": "string"}, "language": {"type": "string"}, "languages_url": {"type": "string"}, "merges_url": {"type": "string"}, "milestones_url": {"type": "string"}, "mirror_url": {"type": "string"}, "name": {"type": "string"}, "network_count": {"type": "integer"}, "node_id": {"type": "string"}, "notifications_url": {"type": "string"}, "open_issues_count": {"type": "integer"}, "_private": {"type": "boolean"}, "pulls_url": {"type": "string"}, "pushed_at": {"type": "string"}, "releases_url": {"type": "string"}, "size": {"type": "integer"}, "ssh_url": {"type": "string"}, "stargazers_count": {"type": "integer"}, "stargazers_url": {"type": "string"}, "statuses_url": {"type": "string"}, "subscribers_count": {"type": "integer"}, "subscribers_url": {"type": "string"}, "subscription_url": {"type": "string"}, "svn_url": {"type": "string"}, "tags_url": {"type": "string"}, "teams_url": {"type": "string"}, "temp_clone_token": {"type": "string"}, "topics": {"type": "object"}, "trees_url": {"type": "string"}, "updated_at": {"type": "string"}, "url": {"type": "string"}, "use_squash_pr_title_as_default": {"type": "boolean"}, "visibility": {"type": "string"}, "watchers_count": {"type": "integer"}}}, "repository": {"type": "object", "properties": {"license": {"$ref": "#/definitions/license"}, "organization": {"$ref": "#/definitions/organization"}, "owner": {"$ref": "#/definitions/owner"}, "permissions": {"$ref": "#/definitions/permissions1"}, "template_repository": {"$ref": "#/definitions/template_repository"}, "allow_auto_merge": {"type": "boolean"}, "allow_forking": {"type": "boolean"}, "allow_merge_commit": {"type": "boolean"}, "allow_rebase_merge": {"type": "boolean"}, "allow_squash_merge": {"type": "boolean"}, "allow_update_branch": {"type": "boolean"}, "archive_url": {"type": "string"}, "archived": {"type": "boolean"}, "assignees_url": {"type": "string"}, "blobs_url": {"type": "string"}, "branches_url": {"type": "string"}, "clone_url": {"type": "string"}, "collaborators_url": {"type": "string"}, "comments_url": {"type": "string"}, "commits_url": {"type": "string"}, "compare_url": {"type": "string"}, "contents_url": {"type": "string"}, "contributors_url": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "default_branch": {"type": "string"}, "delete_branch_on_merge": {"type": "boolean"}, "deployments_url": {"type": "string"}, "description": {"type": "string"}, "disabled": {"type": "boolean"}, "downloads_url": {"type": "string"}, "events_url": {"type": "string"}, "fork": {"type": "boolean"}, "forks": {"type": "integer"}, "forks_count": {"type": "integer"}, "forks_url": {"type": "string"}, "full_name": {"type": "string"}, "git_commits_url": {"type": "string"}, "git_refs_url": {"type": "string"}, "git_tags_url": {"type": "string"}, "git_url": {"type": "string"}, "has_downloads": {"type": "boolean"}, "has_issues": {"type": "boolean"}, "has_pages": {"type": "boolean"}, "has_projects": {"type": "boolean"}, "has_wiki": {"type": "boolean"}, "homepage": {"type": "string"}, "hooks_url": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "is_template": {"type": "boolean"}, "issue_comment_url": {"type": "string"}, "issue_events_url": {"type": "string"}, "issues_url": {"type": "string"}, "keys_url": {"type": "string"}, "labels_url": {"type": "string"}, "language": {"type": "string"}, "languages_url": {"type": "string"}, "master_branch": {"type": "string"}, "merges_url": {"type": "string"}, "milestones_url": {"type": "string"}, "mirror_url": {"type": "string"}, "name": {"type": "string"}, "network_count": {"type": "integer"}, "node_id": {"type": "string"}, "notifications_url": {"type": "string"}, "open_issues": {"type": "integer"}, "open_issues_count": {"type": "integer"}, "_private": {"type": "boolean"}, "pulls_url": {"type": "string"}, "pushed_at": {"type": "string", "format": "date-time"}, "releases_url": {"type": "string"}, "size": {"type": "integer"}, "ssh_url": {"type": "string"}, "stargazers_count": {"type": "integer"}, "stargazers_url": {"type": "string"}, "starred_at": {"type": "string"}, "statuses_url": {"type": "string"}, "subscribers_count": {"type": "integer"}, "subscribers_url": {"type": "string"}, "subscription_url": {"type": "string"}, "svn_url": {"type": "string"}, "tags_url": {"type": "string"}, "teams_url": {"type": "string"}, "temp_clone_token": {"type": "string"}, "topics": {"type": "object"}, "trees_url": {"type": "string"}, "updated_at": {"type": "string", "format": "date-time"}, "url": {"type": "string"}, "use_squash_pr_title_as_default": {"type": "boolean"}, "visibility": {"type": "string"}, "watchers": {"type": "integer"}, "watchers_count": {"type": "integer"}}}, "user": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "email": {"type": "string"}, "events_url": {"type": "string"}, "followers_url": {"type": "string"}, "following_url": {"type": "string"}, "gists_url": {"type": "string"}, "gravatar_id": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "login": {"type": "string"}, "name": {"type": "string"}, "node_id": {"type": "string"}, "organizations_url": {"type": "string"}, "received_events_url": {"type": "string"}, "repos_url": {"type": "string"}, "site_admin": {"type": "boolean"}, "starred_at": {"type": "string"}, "starred_url": {"type": "string"}, "subscriptions_url": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "create_issues_Create": {"type": "object", "properties": {"assignee": {"$ref": "#/definitions/assignee"}, "assignees": {"type": "object"}, "closed_by": {"$ref": "#/definitions/closed_by"}, "labels": {"type": "object"}, "milestone": {"$ref": "#/definitions/milestone"}, "performed_via_github_app": {"$ref": "#/definitions/performed_via_github_app"}, "pull_request": {"$ref": "#/definitions/pull_request"}, "reactions": {"$ref": "#/definitions/reactions"}, "repository": {"$ref": "#/definitions/repository"}, "user": {"$ref": "#/definitions/user"}, "active_lock_reason": {"type": "string"}, "author_association": {"type": "string"}, "body": {"type": "string"}, "body_html": {"type": "string"}, "body_text": {"type": "string"}, "closed_at": {"type": "string", "format": "date-time"}, "comments": {"type": "integer"}, "comments_url": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "draft": {"type": "boolean"}, "events_url": {"type": "string"}, "html_url": {"type": "string"}, "id": {"type": "integer"}, "labels_url": {"type": "string"}, "locked": {"type": "boolean"}, "node_id": {"type": "string"}, "number": {"type": "integer"}, "repository_url": {"type": "string"}, "state": {"type": "string"}, "state_reason": {"type": "string"}, "timeline_url": {"type": "string"}, "title": {"type": "string"}, "updated_at": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}}}, "isArray": false}}}
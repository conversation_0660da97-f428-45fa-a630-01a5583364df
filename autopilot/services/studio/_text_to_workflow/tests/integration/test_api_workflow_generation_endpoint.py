import json
from unittest.mock import patch

import pytest

from services.studio._text_to_workflow.api_workflow.api_workflow_endpoint import generate_workflow, init
from services.studio._text_to_workflow.api_workflow.api_workflow_request_schema import APIWorkflowRequest, APIWorkflowResponse
from services.studio._text_to_workflow.schemas import RequestContext
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.tests.integration.utils.dynamic_activities_mocks import mock_dynamic_activities_config_map
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils import yaml_utils as yu
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent

_api_draft_fixtures_path = conftest._fixtures_path / "api_draft_fixtures"
_api_draft_query_samples_path = _api_draft_fixtures_path / "query_samples.yaml"


@pytest.fixture(scope="module")
def api_draft_query_samples() -> dict:
    return yu.yaml_load(_api_draft_query_samples_path)


@pytest.mark.asyncio
async def test_generate_workflow_endpoint(api_draft_query_samples: dict):
    # Setup test data
    test_data = api_draft_query_samples["basic_api_workflow"]

    # Setup context
    context = RequestContext(email="<EMAIL>", first_name="Test", last_name="Test")
    request_utils.set_request_context(context)

    # Create a mock request
    request = APIWorkflowRequest(
        userRequest=test_data["query"],
        connections=[],  # Empty connections list for simplicity
        existingWorkflow=None,
        expressionLanguage="js",
    )

    # Setup mock for the task
    with (
        patch.object(WorkflowGenerationDynamicActivitiesComponent, "augment_type_definitions_for_dynamic_activities") as mock_augment,
    ):
        # Get the mock dynamic activities config map - keep only the ones we need
        dummy_config_map = {
            k: v
            for k, v in mock_dynamic_activities_config_map().items()
            if k
            in (
                "UiPath.IntegrationService.Activities.Runtime.Activities.GitHubCreate_Issue",
                "UiPath.IntegrationService.Activities.Runtime.Activities.ServiceNowCreate_New_Incident",
            )
        }

        mock_augment.return_value = ({}, dummy_config_map)

        # Initialize the endpoint
        init()

        # Call the function
        response: APIWorkflowResponse = await generate_workflow(request)

        # Assertions
        assert response is not None
        assert response["result"] is not None

        # Test that the resulting workflow structure is correct
        workflow_dict = json.loads(response["result"])
        assert len(workflow_dict["root"]["do"]) == 2
        assert workflow_dict["root"]["do"][0]["activity"] == "GitHubCreate_Issue"
        assert workflow_dict["root"]["do"][0]["metadata"]["uiPathActivityTypeId"] == "f7b2f478-d940-33c1-a56d-5d50e81afe2c"
        assert workflow_dict["root"]["do"][0]["metadata"]["configuration"] is not None
        assert workflow_dict["root"]["do"][1]["activity"] == "ServiceNowCreate_New_Incident"
        assert workflow_dict["root"]["do"][1]["metadata"]["uiPathActivityTypeId"] == "06ca824f-f978-3008-a925-dee245fc6614"
        assert workflow_dict["root"]["do"][1]["metadata"]["configuration"] is not None

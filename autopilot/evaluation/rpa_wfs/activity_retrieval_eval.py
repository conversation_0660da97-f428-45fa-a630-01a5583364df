import asyncio
import pathlib
import time
import typing as t

from evaluation.activity_retrieval_eval_base import ActivityRetrievalEvalServiceBase
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import ActivitySearchOptions, Connection
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.activity_retrieval import (
    ActivityLocationIndex,
    ActivityRetrievalEval,
)
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.dataset import (
    Supported_Excel_Packages,
    Supported_Mail_Packages,
    get_corresponding_pkl_path,
    get_wf_generation_dataset,
)
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import GenerationSettingsBuilder
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.services.helpers.ignored_activities_helper import get_ignored_namespaces
from services.studio._text_to_workflow.workflow_generation.services.helpers.wf_gen_constants import IGNORED_ACTIVITIES, IGNORED_TRIGGERS
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalService,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WfGenDataPointV2


class ActivityRetrievalEvalService(ActivityRetrievalEvalServiceBase):
    activities_retriever: ActivitiesRetriever
    wf_gen_activities_retriever: WorkflowGenerationActivityRetrievalService

    def __init__(
        self,
        wf_gen_activities_retriever: WorkflowGenerationActivityRetrievalService,
        activities_retriever: ActivitiesRetriever,
        embedding_model: EmbeddingModel,
        cache_eval_results: bool = False,  # caching these results might be useful, they can be used for an eval in the draft generation
        batch_size: int = 4,
        retry_count: int = 3,
    ):
        self.activities_retriever = activities_retriever
        self.wf_gen_activities_retriever = wf_gen_activities_retriever

        super().__init__(embedding_model, "activity_retrieval_eval", self.wf_gen_activities_retriever.config, cache_eval_results, batch_size, retry_count)

    def serialize_demonstration(self, demonstration: WfGenDataPointV2) -> str:
        return self.activity_retrieval_templates["sample_row_demonstrations_template"].format(
            query=demonstration["query"],
            plan=demonstration["plan"].replace("\n", "<br>"),
            score=demonstration["score"],
            ambiguities=demonstration["ambiguities"],
            used_activities="<br>".join(demonstration["used_activities"]),
            used_triggers="<br>".join(demonstration["used_triggers"]),
        )

    def _get_baseline_activities(
        self,
        sample: WfGenDataPointV2,
        connections: list[Connection],
        ignored_namespaces: set[str],
    ) -> t.Tuple[set[str], set[str]]:
        """Get the triggers/activities extracted from the source of truth plan within the current dataset"""

        mode = sample["mode"]
        target_framework = sample["target_framework"]

        ignored_activities = self.wf_gen_activities_retriever.ignored_activities[target_framework]

        steps = self._embed_workflow_plan(sample["plan"])

        steps, _, _, _ = self.activities_retriever.get_relevant(
            steps,
            connections,
            ActivitySearchOptions(
                mode=mode,
                target_framework=target_framework,
                ignored_namespaces=ignored_namespaces,
                ignored_activities=ignored_activities,
            ),
        )

        baseline_retrieved_triggers = set()
        if mode == "workflow":
            baseline_retrieved_triggers = set([item["fullClassName"] for item in steps[0]["triggers"] if item["fullClassName"] not in IGNORED_TRIGGERS])
            steps = steps[1:]

        all_activities = [item for step in steps for item in step["activities"]]
        baseline_retrieved_activities = set([item["fullClassName"] for item in all_activities if item["fullClassName"] not in IGNORED_ACTIVITIES])

        return baseline_retrieved_triggers, baseline_retrieved_activities

    async def _evaluate_sample(
        self,
        path: pathlib.Path,
        sample: WfGenDataPointV2,
        connections: list[Connection],
    ) -> ActivityRetrievalEval:
        start = time.time()

        workflow = Workflow("", "", sample["existing_workflow_sequence"]) if sample["existing_workflow_sequence"] is not None else None
        generation_settings = GenerationSettingsBuilder.build_generation_settings(
            sample["query"], sample["target_framework"], None, sample["used_activities"], Supported_Excel_Packages, Supported_Mail_Packages
        )
        retrieval_result: ActivityRetrievalResult = await self.wf_gen_activities_retriever.generate_relevant_activities(
            sample["query"], workflow, connections, sample["mode"], sample["target_framework"], generation_settings, True
        )
        elapsed = time.time() - start

        if self.cache_eval_results:
            # get path to the corresponding .pkl file
            result_path = get_corresponding_pkl_path(path)
            self._serialize_result(result_path, retrieval_result)

        # get the triggers/activities extracted from the source of truth plan within the current dataset
        ignored_namespaces = get_ignored_namespaces(workflow, self.activities_retriever, sample["target_framework"], generation_settings)
        baseline_retrieved_triggers, baseline_retrieved_activities = self._get_baseline_activities(sample, connections, ignored_namespaces)

        # these are the activities/triggers that we expect the model to identify as relevant to build the workflow for the current dataset
        relevant_used_activities = [item for item in sample["used_activities"] if item not in IGNORED_ACTIVITIES]
        relevant_used_triggers = [item for item in sample["used_triggers"] if item not in IGNORED_TRIGGERS]

        raw_matched_activities = set(retrieval_result.unprocessed_retrieved_activities) & set(relevant_used_activities)

        extended_matched_activities = set(retrieval_result.retrieved_activities) & set(relevant_used_activities)
        baseline_matched_activities = baseline_retrieved_activities & set(relevant_used_activities)

        activity_indexes: dict[str, ActivityLocationIndex] = {}
        generation_details = retrieval_result.generation_details

        # for each relevant activity, we will track its location in the proposal dataset, as well as if it was correctly identified
        for activity_full_name in relevant_used_activities:
            activity_indexes[activity_full_name] = ActivityLocationIndex(
                sample["query"],
                activity_full_name in extended_matched_activities,
                self._get_first_match_index(generation_details.query_proposal_activities, activity_full_name),
                self._get_first_match_index(generation_details.workflow_proposal_activities, activity_full_name),
            )

        extended_trigger_precision = None
        raw_trigger_recall = None
        extended_trigger_recall = None
        baseline_trigger_recall = None

        if len(relevant_used_triggers) > 0:
            unprocessed_matched_triggers = set(retrieval_result.unprocessed_retrieved_triggers) & set(relevant_used_triggers)
            extended_matched_triggers = set(retrieval_result.retrieved_triggers) & set(relevant_used_triggers)
            baseline_matched_triggers = set(baseline_retrieved_triggers) & set(relevant_used_triggers)

            activity_indexes[relevant_used_triggers[0]] = ActivityLocationIndex(
                sample["query"],
                relevant_used_triggers[0] in extended_matched_triggers,
                self._get_first_match_index(generation_details.query_proposal_triggers, relevant_used_triggers[0]),
                self._get_first_match_index(generation_details.workflow_proposal_triggers, relevant_used_triggers[0]),
            )

            # for precision, lets consider "all" triggers that were retrieved, not just the correctly identified ones
            total_retrieved_triggers = len(retrieval_result.generation.triggers)

            extended_trigger_precision = len(extended_matched_triggers) / total_retrieved_triggers if total_retrieved_triggers > 0 else 0
            raw_trigger_recall = len(unprocessed_matched_triggers) / len(relevant_used_triggers)

            extended_trigger_recall = len(extended_matched_triggers) / len(relevant_used_triggers)
            baseline_trigger_recall = len(baseline_matched_triggers) / len(relevant_used_triggers)

        # embed the "correct" ambiguities and plan tokens, we will use cosine distance to compare them with the model's output
        embeddings = self.embedding_model.encode_batch(
            [
                sample["ambiguities"] if sample["ambiguities"] is not None else "",
                retrieval_result.generation.ambiguities if retrieval_result.generation.ambiguities is not None else "",
                sample["plan"],
                retrieval_result.generation.plan if retrieval_result.generation.plan is not None else "",
            ],
            4,
        )

        total_retrieved_activities = len(retrieval_result.unprocessed_retrieved_activities)
        total_extended_retrieved_activities = len(retrieval_result.retrieved_activities)

        return ActivityRetrievalEval(
            sample=sample,
            elapsed_time=elapsed,
            # for precision, lets consider "all" activities that were retrieved
            raw_activities_precision=(len(raw_matched_activities) / total_retrieved_activities) if total_retrieved_activities > 0 else 0,
            extended_activities_precision=(len(extended_matched_activities) / total_extended_retrieved_activities)
            if total_extended_retrieved_activities > 0
            else 0,
            raw_activities_recall=(len(raw_matched_activities) / len(relevant_used_activities)) if len(relevant_used_activities) > 0 else None,
            extended_activities_recall=(len(extended_matched_activities) / len(relevant_used_activities)) if len(relevant_used_activities) > 0 else None,
            baseline_activities_recall=(len(baseline_matched_activities) / len(relevant_used_activities)) if len(relevant_used_activities) > 0 else None,
            extended_trigger_precision=extended_trigger_precision,
            extended_trigger_recall=extended_trigger_recall,
            raw_trigger_recall=raw_trigger_recall,
            baseline_trigger_recall=baseline_trigger_recall,
            score_precision=(100 - abs(sample["score"] - max(100, retrieval_result.generation.score))) / 100,
            ambiguities_similarity=embeddings[0] @ embeddings[1],
            plan_similarity=embeddings[2] @ embeddings[3],
            input_activity_indexes=activity_indexes,
            generation_settings=generation_settings,
            proposed_triggers=retrieval_result.proposed_triggers,
            proposed_activities=retrieval_result.proposed_activities,
            retrieved_triggers=retrieval_result.retrieved_triggers,
            retrieved_activities=retrieval_result.retrieved_activities,
            demonstrations=retrieval_result.demonstrations,
            generation=retrieval_result.generation,
            token_usage=retrieval_result.token_usage,
            prompt=retrieval_result.prompt,
        )


if __name__ == "__main__":
    activities_retriever = ActivitiesRetriever()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_retriever, embedding_model)

    wf_gen_activities_retriever = WorkflowGenerationActivityRetrievalService(activities_retriever, connection_embeddings_retriever)
    activity_eval_service = ActivityRetrievalEvalService(wf_gen_activities_retriever, activities_retriever, embedding_model, True, 8)

    tenant_id, connections = get_connections_data()
    dataset_dict = get_wf_generation_dataset(
        ["Portable", "Windows"],
        ["test"],
    )
    result = asyncio.run(activity_eval_service.run(dataset_dict, connections, tenant_id))

import json

from langchain.output_parsers import YamlOutputParser
from langchain.output_parsers.fix import OutputFixingParser
from langchain.output_parsers.format_instructions import YAML_FORMAT_INSTRUCTIONS
from langchain_core.language_models import BaseChatModel
from langchain_core.output_parsers import PydanticOutputParser
from openai import LengthFinishReasonError
from pydantic import BaseModel

from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


def build_pydantic_parser_with_fix(llm: BaseChatModel, pydantic_object: type[BaseModel]) -> OutputFixingParser:
    return OutputFixingParser.from_llm(parser=MinifiedPydanticOutputParser(pydantic_object=pydantic_object), llm=llm)


def is_out_of_tokens(e: Exception, output_type: type) -> bool:
    if hasattr(e, "status_code") and e.status_code == 401 and "The model response did not complete successfully.\\nFinish reason: 2" in str(e):  # type: ignore
        return True

    if (
        hasattr(e, "completion")
        and hasattr(e.completion, "choices")
        and len(e.completion.choices) > 0
        and hasattr(e.completion.choices[0], "message")
        and hasattr(e.completion.choices[0].message, "content")
    ):
        if not settings.IS_PROD:
            LOGGER.warning(f"LengthFinishReasonError - Completion Tokens exceeded for content {e.completion.choices[0].message.content}.")
        else:
            LOGGER.error(f"LengthFinishReasonError - Completion Tokens exceeded for output type {output_type.__name__}.")

    return isinstance(e, LengthFinishReasonError)


class MinifiedPydanticOutputParser(PydanticOutputParser):
    def get_format_instructions(self) -> str:
        # Copy schema to avoid altering original Pydantic schema.
        schema = dict(self.pydantic_object.model_json_schema().items())

        # Remove extraneous fields.
        reduced_schema = schema
        if "title" in reduced_schema:
            del reduced_schema["title"]
        if "type" in reduced_schema:
            del reduced_schema["type"]
        # Ensure json in context is well-formed with double quotes.
        schema_str = json.dumps(reduced_schema, ensure_ascii=False, separators=(",", ":"))

        return _PYDANTIC_FORMAT_INSTRUCTIONS.format(schema=schema_str)


class MinifiedYamlOutputParser(YamlOutputParser):
    """
    Same as the YamlOutputParser from langchain but minifies the output by removing  spaces after commas and colons in the JSON string.
    """

    def get_format_instructions(self) -> str:
        # Copy schema to avoid altering original Pydantic schema.
        schema = {k: v for k, v in self.pydantic_object.schema().items()}

        # Remove extraneous fields.
        reduced_schema = schema
        if "title" in reduced_schema:
            del reduced_schema["title"]
        if "type" in reduced_schema:
            del reduced_schema["type"]
        # Ensure yaml in context is well-formed with double quotes.
        schema_str = json.dumps(reduced_schema, separators=(",", ":"))  # separators help minify the json string

        return YAML_FORMAT_INSTRUCTIONS.format(schema=schema_str)


# Originally from langchain_core.output_parsers.pydantic import _PYDANTIC_FORMAT_INSTRUCTIONS but minified
_PYDANTIC_FORMAT_INSTRUCTIONS = """The output should be formatted as a JSON instance that conforms to the JSON schema below.

As an example, for the schema {{"properties":{{"foo":{{"title":"Foo","description":"a list of strings","type":"array","items":{{"type":"string"}}}}}},"required":["foo"]}}
the object {{"foo":["bar","baz"]}} is a well-formatted instance of the schema. The object {{"properties":{{"foo":["bar","baz"]}}}} is not well-formatted.

Here is the output schema:
```
{schema}
```"""  # noqa: E501

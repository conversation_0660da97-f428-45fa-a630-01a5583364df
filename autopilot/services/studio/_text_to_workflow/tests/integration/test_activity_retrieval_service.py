from unittest.mock import patch

import pytest

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils import yaml_utils as yu
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import GenerationSettings
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval_details import ActivityRetrievalDetails
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalService,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ActivityRetrievalGeneration, ProposedActivity

_activity_retrieval_fixtures_path = conftest._fixtures_path / "activity_retrieval_fixtures"
_activity_retrieval_query_samples_path = _activity_retrieval_fixtures_path / "query_samples.yaml"
_activity_retrieval_invalid_ids_samples_path = _activity_retrieval_fixtures_path / "invalid_ids_samples.yaml"
_query_samples = (
    "basic_deserialization",
    "gsuite_trigger",
)

_invalid_retrieved_ids_samples = (
    "hallucinated_ids",
    "trigger_and_activities_mixup",
)


@pytest.fixture(scope="module")
def activity_config_query_samples() -> dict:
    return yu.yaml_load(_activity_retrieval_query_samples_path)


@pytest.fixture(scope="module")
def activity_config_invalid_ids_samples() -> dict:
    return yu.yaml_load(_activity_retrieval_invalid_ids_samples_path)


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _query_samples)
async def test_activity_retrieval_service(case: str, activity_config_query_samples: dict, activities_retriever: ActivitiesRetriever):
    test_data: dict = activity_config_query_samples[case]
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_retriever, ModelManager().get_embeddings_model("activities_embedding_model"))
    activities_retrieval_service = WorkflowGenerationActivityRetrievalService(activities_retriever, connection_embeddings_retriever)

    result = await activities_retrieval_service.generate_relevant_activities(
        test_data["query"], None, [], test_data["mode"], test_data["target_framework"], GenerationSettings(None, None, None)
    )

    # Check if all expected activities are retrieved
    assert (set(result.retrieved_activities) & set(test_data["expected_activities"])).issuperset(test_data["expected_activities"])

    if test_data["mode"] == "sequence":
        # check no triggers are retrieved for sequence mode
        assert len(result.retrieved_triggers) == 0
    else:
        # Check if all expected triggers are retrieved in workflow mode
        assert (set(result.retrieved_triggers) & set(test_data["expected_triggers"])).issuperset(test_data["expected_triggers"])


def get_dummy_proposed_activity(full_name: str, activity_id: int) -> ProposedActivity:
    return ProposedActivity(
        type_full_name=full_name, description="test", display_name="test", id=activity_id, connectorKey="test", namespace="test", category="test"
    )


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _invalid_retrieved_ids_samples)
async def test_invalid_retrieved_ids(case: str, activity_config_invalid_ids_samples: dict, activities_retriever: ActivitiesRetriever):
    """Test that scenarios where incorrect ids are returned by the model are handled correctly.
    - Hallucinated should be removed
    - Triggers returned as activities should be correctly identified and added to the triggers list AND vice-versa
    """
    test_data: dict = activity_config_invalid_ids_samples[case]
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_retriever, ModelManager().get_embeddings_model("activities_embedding_model"))
    activities_retrieval_service = WorkflowGenerationActivityRetrievalService(activities_retriever, connection_embeddings_retriever)

    with (
        patch("langchain_core.runnables.base.RunnableSequence.ainvoke") as mock_ainvoke,
        patch.object(activities_retrieval_service, "_get_activity_details_for_query") as mock_get_activity_details_for_query,
    ):
        mock_ainvoke.return_value = ActivityRetrievalGeneration(
            plan="",
            ambiguities="",
            score=0,
            triggers=test_data["retrieved_trigger_ids"],
            activities=test_data["retrieved_activity_ids"],
            inexistentActivities=[],
            inexistentTriggers=[],
        )

        # the exact ids are not important for this test - they just need to be unique, the retrieval service will internally assign dummy id values when formatting the prompt
        # for activities the dummy ids will be 1, 2, 3, ... and for triggers the dummy ids will be len(activities) + 1, len(activities) + 2, ...
        # we just need to ensure that we pass enough activities/triggers to the retrieval service to re-create our scenario
        mock_get_activity_details_for_query.return_value = ActivityRetrievalDetails(
            activity_steps=[[get_dummy_proposed_activity(x, i + 1) for i, x in enumerate(test_data["proposed_activities"])]],
            relevant_triggers=[
                get_dummy_proposed_activity(x, i + len(test_data["proposed_activities"]) + 1) for i, x in enumerate(test_data["proposed_triggers"])
            ],
            basic_activities=[],
            basic_triggers=[],
            ground_truth_activities=[],
            ground_truth_triggers=[],
        )

        result = await activities_retrieval_service.generate_relevant_activities(
            "ala bala", None, [], "workflow", "Windows", GenerationSettings(None, None, None)
        )

        assert result.unprocessed_retrieved_activities == test_data["expected_activities"]
        assert result.unprocessed_retrieved_triggers == test_data["expected_triggers"]

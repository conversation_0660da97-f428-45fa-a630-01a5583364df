basic_deserialization:
  target_framework: Portable
  query: Deserialize a JSON string into a JSON array and store it in a variable.
  mode: sequence
  expected_activities:
  - UiPath.Web.Activities.DeserializeJsonArray
  expected_triggers: []
gsuite_trigger:
  target_framework: Portable
  query: Summarize new Gmail email using OpenAI and share the summary via Slack
  mode: workflow
  expected_activities:
  - UiPath.IntegrationService.Activities.Runtime.Activities.UiPath_GenAI_ActivitiesSummarize_Text
  - UiPath.IntegrationService.Activities.Runtime.Activities.SlackSend_Message_to_Channel
  expected_triggers:
  - UiPath.GSuite.Activities.Gmail.Triggers.NewEmailReceived
import json
import os
from typing import Any, Callable, Optional

import pytest

from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_router_task import APIWorkflowRouterTask
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import APIWorkflowAssistantRequest
from services.studio._text_to_workflow.utils.sse_helper import IMessageEmitter

FIXTURES_DIR = os.path.join(os.path.dirname(__file__), "..", "fixtures", "api_workflow_assistant_fixtures")

API_ROUTER_TASK = APIWorkflowRouterTask()


# Create a dummy message emitter for testing
class DummyMessageEmitter(IMessageEmitter):
    async def emit_message_part(self, content: Any, message_id_delta: int) -> int:
        return 0

    async def emit_message(self, content: Any, message_type: str = "message") -> int:
        return 0

    async def emit_debug_message(self, content: Any | Callable[[], Any]) -> int | None:
        return 0

    async def emit_error(self, content: Any, end_stream: bool = True) -> int:
        return 0

    async def emit_completion(self, content: Optional[Any] = None) -> int:
        return 0


async def run_scenario(test_data: dict):
    """Test the workflow router for a specific scenario with test data"""
    # Create a request from the test data
    request_data = test_data["request"]

    # Load expected response
    expected_response = test_data["expectedResponse"]

    # Create a request instance
    request = APIWorkflowAssistantRequest(
        userRequest=request_data["userRequest"],
        messageHistory=request_data.get("messageHistory", []),
        projectDefinition=request_data["projectDefinition"],
        currentWorkflow=request_data.get("currentWorkflow"),
        currentWorkflowDesignerState=request_data.get("currentWorkflowDesignerState"),
        attachments=request_data.get("attachments", []),
        availableEntities=request_data.get("availableEntities", []),
        expressionLanguage=request_data.get("expressionLanguage", ""),
    )

    message_emitter = DummyMessageEmitter()

    # Process the request
    response, _ = await API_ROUTER_TASK._process_internal(request, message_emitter)

    # If the query is invalid, return the response directly
    if not response.valid:
        return response

    assert response.scenario == expected_response["scenario"], f"Expected scenario {expected_response['scenario']}, got {response.scenario}"
    assert response.valid == expected_response["valid"], f"Expected valid {expected_response['valid']}, got {response.valid}"

    # Check the router correctly identified workflow references
    if expected_response.get("workflowRefs"):
        assert response.workflowRefs is not None, "Expected workflowRefs, got None"
        assert set(response.workflowRefs) == set(expected_response["workflowRefs"]), (
            f"Expected workflowRefs {expected_response['workflowRefs']}, got {response.workflowRefs}"
        )

    # Return response for additional checks if needed
    return response


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "fixture_file",
    ["assistant_analyze_workflow.json", "assistant_edit_single_activity.json", "rewrite_workflow.json", "rewrite_workflow2.json"],
)
async def test_api_workflow_router_scenarios(fixture_file):
    """Test the workflow router for different scenarios"""
    # Load the fixture
    test_data = load_dataset(fixture_file)

    # Run the test
    await run_scenario(test_data)


def load_dataset(filename: str) -> dict[str, Any]:
    """Load a workflow fixture from the fixtures directory"""
    fixture_path = os.path.join(FIXTURES_DIR, filename)
    with open(fixture_path, "r") as f:
        return json.load(f)

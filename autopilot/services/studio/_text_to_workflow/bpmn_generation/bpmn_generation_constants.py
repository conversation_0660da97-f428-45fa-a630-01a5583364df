import enum

# Here we define the parent of the BPMN elements. If the element is not a key in the dictionary, it is default element and has standard parent "bpmn:subprocess", "bpmn:process".
# All defined elements are in the format "bpmn:elementName" because we have elements name in different format for JSON and XML
# ALL nodes/ tasks, gateways, events, containers are defined here as default because they can be children of subprocess and process


HIERARCHY_GROUP = {
    "default": ("bpmn:subprocess", "bpmn:process"),
    "root": (),
}

FLOW_ELEMENTS = ("bpmn:sequenceflow", "bpmn:messageflow", "bpmn:association")

ROOT_ELEMENTS = (
    "bpmn:definitions",
    "bpmn:collaboration",
    "bpmn:process",
    "bpmn:subprocess",
    "bpmn:lane",
    "bpmn:laneset",
    "bpmn:childlaneset",
    "bpmn:participant",
)

START_END_ELEMENTS = ("bpmn:startevent", "bpmn:endevent")

REQUIRED_FIELDS_GROUP = {
    # we use baseElement def with this required fields in delete array
    "base": ("id", "type"),
    "flow": ("id", "type", "source", "target"),
    "default": ("id", "type", "parentId"),
}

DEFAULT_ELEMENT_REQUIRMENETS = {"required": REQUIRED_FIELDS_GROUP["default"], "parent": HIERARCHY_GROUP["default"]}

SUPPORTED_ELEMENTS = {
    # nodes / task
    "bpmn:task": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:usertask": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:servicetask": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:sendtask": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:receivetask": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:manualtask": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:scripttask": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:businessruletask": DEFAULT_ELEMENT_REQUIRMENETS,
    # Gateways
    "bpmn:exclusivegateway": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:parallelgateway": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:inclusivegateway": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:eventbasedgateway": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:complexgateway": DEFAULT_ELEMENT_REQUIRMENETS,
    # Artifacts
    "bpmn:textannotation": {"required": REQUIRED_FIELDS_GROUP["default"], "parent": ("bpmn:subprocess", "bpmn:process", "bpmn:collaboration")},
    # Flows
    "bpmn:sequenceflow": {"required": REQUIRED_FIELDS_GROUP["flow"], "parent": HIERARCHY_GROUP["default"]},
    "bpmn:messageflow": {"required": REQUIRED_FIELDS_GROUP["flow"], "parent": ("bpmn:collaboration", "bpmn:participant")},
    "bpmn:association": {"required": REQUIRED_FIELDS_GROUP["flow"], "parent": HIERARCHY_GROUP["default"]},
    # Events
    "bpmn:startevent": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:endevent": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:intermediatecatchevent": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:intermediatethrowevent": DEFAULT_ELEMENT_REQUIRMENETS,
    "bpmn:boundaryevent": {
        "required": REQUIRED_FIELDS_GROUP["default"],
        "parent": (
            "bpmn:task",
            "bpmn:usertask",
            "bpmn:servicetask",
            "bpmn:sendtask",
            "bpmn:receivetask",
            "bpmn:manualtask",
            "bpmn:scripttask",
            "bpmn:businessruletask",
            "bpmn:subprocess",
        ),
    },
    # Containers
    "bpmn:participant": {"required": REQUIRED_FIELDS_GROUP["base"], "dataRequired": ("processRef",), "parent": ("bpmn:collaboration",)},
    "bpmn:collaboration": {"required": REQUIRED_FIELDS_GROUP["base"], "parent": HIERARCHY_GROUP["root"]},
    "bpmn:lane": {"required": REQUIRED_FIELDS_GROUP["base"], "parent": ("bpmn:laneset", "bpmn:childlaneset")},
    "bpmn:laneset": {"required": REQUIRED_FIELDS_GROUP["base"], "parent": HIERARCHY_GROUP["default"]},
    "bpmn:childlaneset": {"required": REQUIRED_FIELDS_GROUP["base"], "parent": ("bpmn:lane",)},
    "bpmn:subprocess": {"required": REQUIRED_FIELDS_GROUP["base"], "parent": ("bpmn:lane", "bpmn:subprocess", "bpmn:process")},
    "bpmn:process": {"required": REQUIRED_FIELDS_GROUP["base"], "parent": HIERARCHY_GROUP["root"]},
    "bpmn:definitions": {"required": REQUIRED_FIELDS_GROUP["base"], "parent": HIERARCHY_GROUP["root"]},
}

CANNOT_PROCESS_REQUEST_EXPLANATION = "I couldn’t process your request. Try rewording it for better results."


class TelemetryConstants:
    TYPE = "type"
    USER_ACTION = "userAction"
    FILE_ID = "fileId"
    BPMN_DESCRIPTION = "bpmnDescription"
    DOMAIN = "domain"


class ExtensionTypes:
    ACTION = "Actions.HITL"
    PROCESS = "Orchestrator.StartJob"
    AGENT = "Orchestrator.StartAgentJob"
    EXTERNAL_AGENT = "Intsvc.AsyncAgentExecution"
    CONNECTOR_ACTIVITY = "Intsvc.WaitForEvent"
    CONNECTOR_EVENT = "Intsvc.EventTrigger"
    AGENTIC_PROCESS = "Orchestrator.StartAgenticProcessAsync"
    WAIT_AGENTIC_PROCESS = "Orchestrator.StartAgenticProcess"
    API_WORKFLOW = "Orchestrator.ExecuteApiWorkflowAsync"
    BUSINESS_RULE = "Orchestrator.BusinessRules"
    CREATE_WAIT_QUEUE_ITEM = "Orchestrator.CreateAndWaitForQueueItem"
    CREATE_QUEUE_ITEM = "Orchestrator.CreateQueueItem"


class ImplementationTypes(enum.Enum):
    HITL = "Human In The Loop"
    AGENT = "Agents"
    AGENTIC_PROCESS = "Agentic processes"
    API_WORKFLOW = "API workflows"
    BUSINESS_RULE = "Business rules"
    QUEUE = "Queues"
    PROCESS = "Processes"


class ResourceKinds(enum.Enum):
    APP = "app"
    PROCESS = "process"
    QUEUE = "queue"


class ResourceTypes(enum.Enum):
    WORKFLOW_ACTION = "Workflow Action"
    VB_ACTION = "VB Action"
    AGENT = "agent"
    PROCESS_ORCHESTRATION = "processOrchestration"
    API = "api"
    PROCESS = "process"


class BpmnTaskTypes:
    USER = "bpmn:userTask"
    SERVICE = "bpmn:serviceTask"
    SEND = "bpmn:sendTask"
    RECEIVE = "bpmn:receiveTask"
    CALL_ACTIVITY = "bpmn:callActivity"
    BUSINESS_RULE = "bpmn:businessRuleTask"

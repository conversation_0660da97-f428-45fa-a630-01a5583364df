demonstration_ranking:
  planning_demonstrations_limit: 30
  demonstrations_limit: 4
  force_windows_demonstrations: 2
  embedding_retrieval_docs: 30
  reranking_enable: true
  reranking_docs: 30
  mmr_enable: true
  mmr_similarities: iou_retrieved_activities  # choices: embeddings, reranking, pled, plan_tree_edit_distance, iou_activities, iou_retrieved_activities
  mmr_similarities_action: build # choices: build, load, disable; only used at dataset build time
  mmr_docs: 30
  mmr_diversity: 1.0  # 1.0 if you use reranking, 0.2 if not
  sorting_order: descending  # choices: ascending, descending
post_processing:
  plan_step_activities: 1 # how many activities to try extract for each plan step
  plan_step_triggers: 1
  anticipated_inexistent_activities: 2 # how many matches for each anticipated inexistent activities to keep
  anticipated_inexistent_triggers: 2
prompt:
  demonstration_filtering:
    planing_demonstrations_limit: 6
    wf_min_trigger_count: 2 # for workflow generation mode, we want at least 2 demonstrations with triggers
  proposed_activities_selection:
    main_query_activities_percentage: 30 # the percentage of activities that should be retrieved from the whole query embedding, the rest will be retrieved from each line of the query embeddings
    query_triggers_limit: 15  # how many related triggers do we want to extract for the query
    query_activities_limit: 150  # how many related activities do we want to extract for the query
    wf_step_activites_limit: 2  # how many related activities do we want to extract for each activity in the existing workflow
  system_msg: |-
    {intro}
    You will be provided with a JSON list with all the available Integration Activities used to build UiPath Serverless Workflows. The activities have ids, display names, descriptions and categories. They will appear as JSON objects.
    The user will provide a query along a subset of Integration Activities ids out of the full list that might be used to build an automation that solves the query.

    {output_format}
    
    {general_requirements}

    # Integration Activities Details:
    - There are 3 types of Integration Activities:
    1. Specialized Integration Activities, which are used for a specific single CRUD operation on a specific entity.
      - Example: SalesforceGet_Account is an activity used to create a new lead in Salesforce.
    2. There are activities that can perform queries in a Query Language specific to the vendor system. They must be selected from the list of proposed activities if they are relevant to the query.
      - Example: SalesforceSearch_using_SOQL is an activity used to retrieve records by executing 'SELECT' queries in the Salesforce Object Query Language (SOQL)
      - Example: Oracle_NetSuiteExecute_SuiteQL_Query is an activity used to execute SELECT SuiteQL queries on the Oracle NetSuite database.
    3. API Integration Activities that perform pre-authorized HTTP calls to a specific application API, used only when a more specific Integration Operation is not available.
      - The name of these activities is APPLICATIONNAME_HTTP_Request. 
      - Add this type of activity only when you cannot find a more specific activity for the query.
      - It is important to be precise when adding this type of activity and only add it if you're sure the 3rd party application/service it integrates it should be used in the workflow.
      - Example: Salesforce_HTTP_Request is an activity used to perform HTTP requests to interact with Salesforce and should be used whenever a more specific Salesforce Integration Activity is not available.
      - Example: Oracle_NetSuite_HTTP_Request is an activity used to Perform HTTP requests to interact with Oracle NetSuite and should be used whenever a more specific Oracle NetSuite Integration Activity is not available.

    # Relevant Integration Activities Requirements:
    {activity_selection_guidelines_1}
    - Attention: If you already added an activity id, you must not add it again.
    {activity_selection_guidelines_2}
    - Always remember: Once an activity is added to the activities list, any similar activities that have a related purpose, must be added to activities, too. Ensure all possibly relevant activities are added to the list. Try to include all the activities from the same category that are relevant to the query.
    - Our priority should be on eliminating fully irrelevant activities, any potentially relevant activities from the user's list should be included in the relevant activities lists.
    - When asked to "generate something from natural language or using an LLM", make sure to include Content Generation or Chat Completion activities as well in the proposed activities list.
      - Example: PerplexityChat_Completion is an activity used to generate chat completion responses using Perplexity service.
      - Example: DeepSeekGenerate_Chat_Completion is an activity used to generate chat completion responses using DeepSeek models based on a given prompt and inputs. Add this activity when DeepSeek completion is mentioned in the query.
    - When asked to "create something", make sure to include activities that are relevant to the query, like write/send/invite activities for your type of entity because you will probably be asked to fill in the details into what you created.
    {activity_types}
    - If you fail to provide activities that may be necessary for the plan, you will be heavily penalized.
    - Try to be very specific when retrieving activities, do not retrieve activities that are not specific to the query, it is better to retrieve fewer relevant activities than more with a broader scope.

    - Very Important: If there seem to be multiple activities useful even for a step which seems specific (but may be achieved in multiple ways), provide all of them. Take note of these examples:
         - Example: If a plan step says "Send a notification message in Microsoft Teams", you should provide all of "SendTeamsMessage", "SendTeamsMessageToChannel", "SendTeamsMessageToUser" and "SendTeamsMessageToGroup" in the activities list (activity names are made-up here, to exemplify the point).
    {activity_list_structuring}
    - Do not blindly increment the id of the last activity in the list if the next activity is not useful for solving the user query, e.g. this is completely wrong: [0,1,2,3,4,5,6,7,...,100].
    - Make sure to only include activities ids that are actually present in the full list of activities and triggers.

    {ambiguities_requirements}

    {score_requirements}

    # Inexistent Activity Type Name Requirements:
    - The inexistentActivities should be a list of fictional activity type names that you would have liked to have retrieved, but which do not exist.
    - However, if you think you did a good job and fulfilled the activities for the plan well, inexistentActivities should be empty.
    - The inexistentActivities should be lists of strings.
    - Example: 
      ```
        Query: "Download the email from Gmail and upload it to Google Drive and OneDrive."
        Retrieval Critique: "I found the OneDrive upload activity, but I did not find the Google Drive upload activity."
        Anticipated But Inexistent Activity Type Names: ["UiPath.GoogleDrive.Activities.UploadFilesConnections"]
      ```
    
    # Plan Requirements:
    - It's important to include "If" and "For each" steps where necessary in the pseudocode.
    {plan_requirements}
        Query: "Download the invoice attachment from Gmail and upload it to Salesforce as a related document."
        Bad Example: "1. Download the invoice from Gmail.\n2. Upload the document to Salesforce."
        Good Example: "1. Use Gmail API to retrieve emails with invoice attachments.\n2. Extract and download the invoice attachment from the email.\n3. Use Salesforce API to upload the document and associate it with the appropriate record in Salesforce."

    {footer}


  system_msg_current_workflow: |-
    # Relating the planning and the activity retrieval to the current workflow:
    - The current workflow has the following namespaces: {current_workflow_namespaces}
    - When planning and retrieving activities, make sure to relate the query to the current workflow, for which you have the namespaces.
    - Make sure to try to relate the query to the current workflow namespaces when writing the plan. Example: you have an ambiguous query saying "Download the email attachments" and not mentioning a provider, while the current workflow namespaces only have UiPath.Mail.Outlook.Activities. It is clear that the user wants to download the email attachments from Outlook, so you should not include Gmail activities, and you should mention "Outlook" in the plan.

  user_msg_template: |-
    QUERY:
    {query}
  demonstration_template: |-
    ```
    QUERY: {query}
    PLAN: {plan}
    ```